<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug CoinFlipper</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #333;
        }
    </style>
</head>
<body>
    <h1>🔍 CoinFlipper Debug</h1>
    
    <div id="debug-output"></div>
    
    <canvas id="testCanvas" width="400" height="400"></canvas>
    
    <button onclick="testCoinFlipper()">Test CoinFlipper</button>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <script>
        const debugOutput = document.getElementById('debug-output');
        
        function addDebugInfo(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `debug-info ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            debugOutput.appendChild(div);
            console.log(message);
        }
        
        // Check Three.js
        if (typeof THREE !== 'undefined') {
            addDebugInfo('✅ Three.js loaded successfully', 'success');
            addDebugInfo(`Three.js version: ${THREE.REVISION}`, 'success');
        } else {
            addDebugInfo('❌ Three.js not loaded', 'error');
        }
        
        // Load CoinFlipper
        const script = document.createElement('script');
        script.src = '../src/coin-flipper.js';
        script.onload = function() {
            addDebugInfo('✅ CoinFlipper script loaded', 'success');
            
            // Check if CoinFlipper class is available
            if (typeof CoinFlipper !== 'undefined') {
                addDebugInfo('✅ CoinFlipper class available', 'success');
            } else {
                addDebugInfo('❌ CoinFlipper class not available', 'error');
            }
        };
        script.onerror = function() {
            addDebugInfo('❌ Failed to load CoinFlipper script', 'error');
        };
        document.head.appendChild(script);
        
        async function testCoinFlipper() {
            try {
                addDebugInfo('🧪 Testing CoinFlipper initialization...');
                
                if (typeof CoinFlipper === 'undefined') {
                    addDebugInfo('❌ CoinFlipper class not available', 'error');
                    return;
                }
                
                const coinFlipper = new CoinFlipper('testCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });
                
                addDebugInfo('✅ CoinFlipper instance created', 'success');
                
                // Wait for ready
                await coinFlipper.ready();
                addDebugInfo('✅ CoinFlipper ready', 'success');
                
                // Start idle
                await coinFlipper.startIdle();
                addDebugInfo('✅ Idle animation started', 'success');
                
                // Test flip after 2 seconds
                setTimeout(async () => {
                    try {
                        addDebugInfo('🎲 Testing coin flip...');
                        const result = await coinFlipper.toss();
                        addDebugInfo(`✅ Flip result: ${result}`, 'success');
                    } catch (error) {
                        addDebugInfo(`❌ Flip failed: ${error.message}`, 'error');
                    }
                }, 2000);
                
            } catch (error) {
                addDebugInfo(`❌ Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }
        
        // Auto-test after 1 second
        setTimeout(() => {
            if (typeof CoinFlipper !== 'undefined') {
                testCoinFlipper();
            }
        }, 1000);
    </script>
</body>
</html>
