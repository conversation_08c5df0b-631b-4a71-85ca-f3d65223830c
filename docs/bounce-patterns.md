# Bounce Patterns Feature

ฟีเจอร์ **Bounce Patterns** ช่วยให้คุณสามารถกำหนด pattern การเด้งของเหรียญแทนการสุ่ม เพื่อให้การเด้งมีลักษณะที่คาดเดาได้และสอดคล้องกับผลลัพธ์ที่ต้องการ

## 🎯 ความสามารถหลัก

### 1. **Pattern Mode vs Random Mode**
- **Random Mode** (เริ่มต้น): เลือกจำนวนการเด้งแบบสุ่มจาก bounce sets
- **Pattern Mode**: ใช้ pattern ที่กำหนดไว้สำหรับแต่ละผลลัพธ์

### 2. **การกำหนด Pattern แยกตามผลลัพธ์**
- **Heads Pattern**: กำหนดการเด้งสำหรับผลลัพธ์ "หัว"
- **Tails Pattern**: กำหนดการเด้งสำหรับผลลัพธ์ "ก้อย"

### 3. **ค่าที่สามารถกำหนดได้**
- **จำนวนการเด้ง** (count): จำนวนครั้งที่เหรียญจะเด้ง
- **การลดพลังงาน** (damping): ค่าการลดพลังงานในแต่ละครั้งที่เด้ง (0-1)
- **แรงโน้มถ่วง** (gravity): ความแรงของแรงโน้มถ่วง (ค่าลบ)
- **การตั้งค่าเสียง** (audioTiming): ความล่าช้าและจังหวะของเสียงการเด้ง

## 📝 วิธีการใช้งาน

### การเปิด/ปิด Pattern Mode

```javascript
// เปิดใช้ Pattern Mode
coinFlipper.enableBouncePatterns(true);

// ปิดใช้ Pattern Mode (กลับไปใช้การสุ่ม)
coinFlipper.enableBouncePatterns(false);

// ตรวจสอบสถานะปัจจุบัน
const isUsingPatterns = coinFlipper.isUsingBouncePatterns();
```

### การตั้งค่า Pattern

```javascript
// ตั้งค่า pattern สำหรับ "หัว"
coinFlipper.setBouncePattern('heads', {
    count: 4,           // เด้ง 4 ครั้ง
    damping: 0.75,      // ลดพลังงาน 25% ต่อครั้ง
    gravity: -0.022,    // แรงโน้มถ่วงแรงกว่าปกติ
    audioTiming: {
        baseDelay: 0.35,    // เริ่มเสียงหลัง 0.35 วินาที
        interval: 0.22,     // ช่วงเวลาระหว่างเสียงเด้ง
        decayFactor: 0.88   // การลดลงของช่วงเวลา
    }
});

// ตั้งค่า pattern สำหรับ "ก้อย"
coinFlipper.setBouncePattern('tails', {
    count: 6,           // เด้ง 6 ครั้ง
    damping: 0.65,      // ลดพลังงาน 35% ต่อครั้ง
    gravity: -0.018,    // แรงโน้มถ่วงอ่อนกว่าปกติ
    audioTiming: {
        baseDelay: 0.4,     // เริ่มเสียงหลัง 0.4 วินาที
        interval: 0.28,     // ช่วงเวลาระหว่างเสียงเด้ง
        decayFactor: 0.82   // การลดลงของช่วงเวลา
    }
});
```

### การดึงข้อมูล Pattern

```javascript
// ดึงข้อมูล patterns ทั้งหมด
const patterns = coinFlipper.getBouncePatterns();
console.log(patterns);
// Output: { heads: {...}, tails: {...} }

// ดึงข้อมูล bounce sets (สำหรับ random mode)
const bounceSets = coinFlipper.getBounceSets();
console.log(bounceSets);
// Output: { heads: [4, 8], tails: [6] }
```

## 🎮 ตัวอย่างการใช้งาน

### ตัวอย่างที่ 1: Pattern แบบต่างกัน

```javascript
// สร้าง CoinFlipper พร้อม patterns
const coinFlipper = new CoinFlipper('canvas', {
    usePatterns: true,  // เปิดใช้ patterns ตั้งแต่เริ่มต้น
    bouncePatterns: {
        heads: {
            count: 3,
            damping: 0.8,
            gravity: -0.025,
            audioTiming: { baseDelay: 0.3, interval: 0.2, decayFactor: 0.9 }
        },
        tails: {
            count: 7,
            damping: 0.6,
            gravity: -0.015,
            audioTiming: { baseDelay: 0.45, interval: 0.3, decayFactor: 0.8 }
        }
    }
});

// ทอยเหรียญ - จะใช้ pattern ที่กำหนด
const result = await coinFlipper.flip('heads');  // จะเด้ง 3 ครั้งเสมอ
```

### ตัวอย่างที่ 2: สลับระหว่าง Pattern และ Random

```javascript
// เริ่มด้วย random mode
coinFlipper.enableBouncePatterns(false);
await coinFlipper.flip('heads');  // จะสุ่มจาก bounce sets

// เปลี่ยนเป็น pattern mode
coinFlipper.enableBouncePatterns(true);
await coinFlipper.flip('heads');  // จะใช้ pattern ที่กำหนด

// ตั้งค่า pattern ใหม่
coinFlipper.setBouncePattern('heads', {
    count: 5,
    damping: 0.7,
    gravity: -0.02
});
await coinFlipper.flip('heads');  // จะเด้ง 5 ครั้งเสมอ
```

## 🔧 ค่าที่แนะนำ (ระบบใหม่: ความสูงและความเร็วหมุน)

### สำหรับ "หัว" (Heads)
```javascript
{
    count: 3-5,         // เด้งน้อยครั้ง = ดูมั่นคง
    maxHeight: 3.8-4.2, // ความสูงสูงสุดที่เหรียญจะขึ้นไป
    rotationSpeed: 0.85-0.95, // ความเร็วในการหมุนขณะโยนขึ้น (radians per frame)
    audioTiming: {
        baseDelay: 0.3-0.35,
        interval: 0.2-0.25,
        decayFactor: 0.85-0.9
    }
}
```

### สำหรับ "ก้อย" (Tails)
```javascript
{
    count: 6-8,         // เด้งหลายครั้ง = ดูไม่แน่นอน
    maxHeight: 3.0-3.4, // ความสูงสูงสุดที่เหรียญจะขึ้นไป
    rotationSpeed: 0.65-0.75, // ความเร็วในการหมุนขณะโยนขึ้น (radians per frame)
    audioTiming: {
        baseDelay: 0.4-0.45,
        interval: 0.25-0.3,
        decayFactor: 0.8-0.85
    }
}
```

### 🆕 การเปลี่ยนแปลงจากระบบเก่า
- **เก่า**: ใช้ `gravity` และ `damping` ในการควบคุมการเด้ง
- **ใหม่**: ใช้ `maxHeight` และ `rotationSpeed` เพื่อให้เห็นความแตกต่างที่ชัดเจน
- **ประโยชน์**:
  - เห็นความสูงของการโยนที่แตกต่างกัน
  - เห็นความเร็วในการหมุนที่แตกต่างกัน
  - ยังคงป้องกันเหรียญจมพื้นด้วยระบบ collision detection

## 🎵 การตั้งค่าเสียง (Audio Timing)

### พารามิเตอร์เสียง
- **baseDelay**: เวลาที่เริ่มเล่นเสียงเด้งครั้งแรก (วินาที)
- **interval**: ช่วงเวลาระหว่างเสียงเด้งแต่ละครั้ง (วินาที)
- **decayFactor**: อัตราการลดลงของช่วงเวลา (0-1)

### ตัวอย่างเสียงที่แตกต่างกัน

```javascript
// เสียงเด้งเร็วและกระชับ (สำหรับ heads)
audioTiming: {
    baseDelay: 0.3,     // เริ่มเร็ว
    interval: 0.2,      // ช่วงสั้น
    decayFactor: 0.9    // ลดลงเร็ว
}

// เสียงเด้งช้าและยาวนาน (สำหรับ tails)
audioTiming: {
    baseDelay: 0.45,    // เริ่มช้า
    interval: 0.3,      // ช่วงยาว
    decayFactor: 0.8    // ลดลงช้า
}
```

## 🚀 ข้อดีของ Pattern Mode

1. **ความสม่ำเสมอ**: ผลลัพธ์เดียวกันจะมีการเด้งแบบเดียวกันเสมอ
2. **การควบคุม**: สามารถปรับแต่งประสบการณ์ให้เหมาะกับเกมหรือแอปพลิเคชัน
3. **ความน่าเชื่อถือ**: ผู้ใช้สามารถคาดเดาพฤติกรรมได้
4. **การออกแบบเสียง**: สามารถปรับเสียงให้เข้ากับธีมของแอปพลิเคชัน

## 📋 ตัวอย่างไฟล์

ดูตัวอย่างการใช้งานแบบเต็มได้ที่:
- `examples/bounce-patterns-example.html` - ตัวอย่างการใช้งานแบบ interactive
- `examples/pattern-comparison.html` - เปรียบเทียบ pattern vs random mode

## 🔄 การย้อนกลับ

หากต้องการกลับไปใช้ระบบเดิม (random mode):

```javascript
// ปิดใช้ patterns
coinFlipper.enableBouncePatterns(false);

// หรือตั้งค่าใน constructor
const coinFlipper = new CoinFlipper('canvas', {
    usePatterns: false  // ใช้ random mode
});
```
