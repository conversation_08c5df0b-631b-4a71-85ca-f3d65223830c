# 📝 Changelog: Bounce Sets Feature

## 🎯 สรุปการเปลี่ยนแปลง

เพิ่มฟีเจอร์ใหม่ที่ให้ผู้ใช้สามารถกำหนด set ของจำนวนการ bounce สำหรับผลลัพธ์แต่ละแบบ (หัว/ก้อย) ได้

## 🔧 การเปลี่ยนแปลงในโค้ด

### 1. AudioManager Class

#### เพิ่ม/แก้ไข:
- **`generateFlipSound(bounceCount)`**: รับพารามิเตอร์ `bounceCount` เพื่อกำหนดจำนวนการเด้ง
- **`generateBounceTimes(count)`**: เมธอดใหม่สำหรับสร้าง bounce times array ตามจำนวนที่กำหนด

```javascript
// เดิม
generateFlipSound() {
    const bounceTimes = [0.4, 0.7, 1.0, 1.2, 1.4, 1.55, 1.68];
    // ...
}

// ใหม่
generateFlipSound(bounceCount = null) {
    let bounceTimes;
    if (bounceCount !== null) {
        bounceTimes = this.generateBounceTimes(bounceCount);
    } else {
        bounceTimes = [0.4, 0.7, 1.0, 1.2, 1.4, 1.55, 1.68];
    }
    // ...
}
```

### 2. CoinRenderer Class

#### เพิ่ม/แก้ไข:
- **Options**: เพิ่ม `bounceSets` configuration
- **Physics Constants**: เพิ่ม `targetBounces` property
- **`selectBounceCount(result)`**: เมธอดใหม่สำหรับเลือกจำนวน bounce จาก set
- **`flipCoin()`**: ปรับให้เลือกจำนวน bounce ตามผลลัพธ์
- **Bounce Logic**: ใช้ `targetBounces` แทน `maxBounces` ในการตัดสินใจหยุดการเด้ง

```javascript
// เพิ่มใน options
this.options = {
    // ...
    bounceSets: {
        heads: [3, 5, 7],
        tails: [4, 6, 8]
    },
    // ...
};

// เพิ่ม physics property
this.targetBounces = 7; // จำนวนการเด้งเป้าหมายสำหรับการโยนปัจจุบัน

// เมธอดใหม่
selectBounceCount(result) {
    const bounceSets = this.options.bounceSets;
    let bounceSet;
    
    if (result === 'heads' && bounceSets.heads && bounceSets.heads.length > 0) {
        bounceSet = bounceSets.heads;
    } else if (result === 'tails' && bounceSets.tails && bounceSets.tails.length > 0) {
        bounceSet = bounceSets.tails;
    } else {
        bounceSet = [5, 6, 7, 8];
    }
    
    const randomIndex = Math.floor(Math.random() * bounceSet.length);
    return bounceSet[randomIndex];
}
```

### 3. CoinFlipper Class (Main)

#### เพิ่ม/แก้ไข:
- **Options**: เพิ่ม `bounceSets` configuration
- **`setBounceSets(bounceSets)`**: เมธอดใหม่สำหรับอัพเดท bounce sets
- **`getBounceSets()`**: เมธอดใหม่สำหรับดึงค่า bounce sets ปัจจุบัน
- **`toss()`**: ปรับให้คำนวณและส่งจำนวน bounce ไปยัง AudioManager

```javascript
// เพิ่มใน options
this.options = {
    // ...
    bounceSets: {
        heads: [3, 5, 7],
        tails: [4, 6, 8]
    },
    // ...
};

// เมธอดใหม่
setBounceSets(bounceSets) {
    if (bounceSets && typeof bounceSets === 'object') {
        this.options.bounceSets = {
            heads: Array.isArray(bounceSets.heads) ? bounceSets.heads : this.options.bounceSets.heads,
            tails: Array.isArray(bounceSets.tails) ? bounceSets.tails : this.options.bounceSets.tails
        };
        
        if (this.coinRenderer) {
            this.coinRenderer.options.bounceSets = this.options.bounceSets;
        }
    }
}

getBounceSets() {
    return { ...this.options.bounceSets };
}
```

## 📁 ไฟล์ใหม่ที่เพิ่ม

1. **`examples/bounce-sets-example.html`**: ตัวอย่างการใช้งานแบบเต็ม
2. **`test-bounce-sets.html`**: ไฟล์ทดสอบฟีเจอร์
3. **`BOUNCE_SETS_FEATURE.md`**: เอกสารอธิบายฟีเจอร์
4. **`CHANGELOG_BOUNCE_SETS.md`**: ไฟล์นี้

## 🎮 วิธีการใช้งาน

### การตั้งค่าเริ่มต้น
```javascript
const coinFlipper = new CoinFlipper('canvasId', {
    bounceSets: {
        heads: [3, 5, 7],   // หัว: สุ่มจาก 3, 5, หรือ 7 ครั้ง
        tails: [4, 6, 8]    // ก้อย: สุ่มจาก 4, 6, หรือ 8 ครั้ง
    }
});
```

### การเปลี่ยนแปลง Bounce Sets
```javascript
coinFlipper.setBounceSets({
    heads: [2, 4, 6],
    tails: [3, 5, 7, 9]
});
```

### การทอยเหรียญ
```javascript
// สุ่มทอย
const result = await coinFlipper.toss();

// บังคับผลลัพธ์
const headsResult = await coinFlipper.toss('heads');
const tailsResult = await coinFlipper.toss('tails');
```

## 🔍 การทดสอบ

1. เปิด `test-bounce-sets.html` ในเบราว์เซอร์
2. ทดสอบการเปลี่ยนแปลง bounce sets
3. ทดสอบการทอยแบบสุ่มและบังคับผลลัพธ์
4. ตรวจสอบ console logs เพื่อดูการทำงาน

## 🐛 การแก้ไขปัญหาที่อาจเกิดขึ้น

### ปัญหาที่อาจพบ:
1. **เสียงไม่ตรงกับการเด้ง**: ตรวจสอบการส่ง bounceCount ไปยัง AudioManager
2. **การเด้งไม่หยุด**: ตรวจสอบค่าใน bounce sets
3. **ไม่สามารถอัพเดท bounce sets**: ตรวจสอบการ initialize

### วิธีแก้ไข:
- ตรวจสอบ console logs
- ใช้ `coinFlipper.getBounceSets()` เพื่อดูค่าปัจจุบัน
- ตรวจสอบว่า arrays ไม่ว่างและมีเฉพาะตัวเลขบวก

## ✅ สิ่งที่ทำงานได้แล้ว

- ✅ กำหนด bounce sets สำหรับ heads และ tails แยกกัน
- ✅ การสุ่มเลือกจำนวน bounce จาก set
- ✅ เสียงที่สอดคล้องกับจำนวน bounce
- ✅ การอัพเดท bounce sets แบบ dynamic
- ✅ API สำหรับการตั้งค่าและดึงค่า
- ✅ ตัวอย่างการใช้งานและเอกสาร

## 🚀 การพัฒนาต่อไป

ฟีเจอร์นี้พร้อมใช้งานแล้ว สามารถนำไปใช้ในโปรเจกต์ได้ทันที หรือปรับแต่งเพิ่มเติมตามความต้องการ
