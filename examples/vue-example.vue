<template>
  <div class="coin-flipper-demo">
    <h2>🪙 Coin Flipper Demo</h2>
    
    <!-- Canvas สำหรับแสดงเหรียญ 3D -->
    <div class="coin-container">
      <canvas 
        ref="coinCanvas" 
        id="coinCanvas"
        width="400" 
        height="400"
        class="coin-canvas"
      ></canvas>
    </div>

    <!-- ข้อมูลสถานะ -->
    <div class="status-info">
      <p><strong>Status:</strong> {{ statusText }}</p>
      <p><strong>Last Result:</strong> {{ lastResult || 'None' }}</p>
      <p><strong>Balance:</strong> ${{ balance }}</p>
    </div>

    <!-- ปุ่มควบคุม -->
    <div class="controls">
      <div class="idle-controls">
        <button 
          @click="startIdle" 
          :disabled="status.isFlipping || status.isIdle"
          class="btn btn-secondary"
        >
          🔄 Start Idle
        </button>
        <button 
          @click="stopIdle" 
          :disabled="status.isFlipping || !status.isIdle"
          class="btn btn-secondary"
        >
          ⏹️ Stop Idle
        </button>
      </div>

      <div class="betting-controls">
        <div class="bet-amount">
          <label>Bet Amount: $</label>
          <input 
            v-model.number="betAmount" 
            type="number" 
            min="1" 
            :max="balance"
            :disabled="status.isFlipping"
          >
        </div>
        
        <div class="bet-choice">
          <label>Choose:</label>
          <select v-model="betChoice" :disabled="status.isFlipping">
            <option value="heads">👑 Heads</option>
            <option value="tails">🦅 Tails</option>
          </select>
        </div>

        <button 
          @click="flipCoin" 
          :disabled="status.isFlipping || betAmount > balance || betAmount < 1"
          class="btn btn-primary"
        >
          {{ status.isFlipping ? '🎲 Flipping...' : '🎲 Flip Coin!' }}
        </button>
      </div>

      <div class="test-controls">
        <h4>Test Controls:</h4>
        <button @click="testHeads" :disabled="status.isFlipping" class="btn btn-test">
          👑 Test Heads
        </button>
        <button @click="testTails" :disabled="status.isFlipping" class="btn btn-test">
          🦅 Test Tails
        </button>
        <button @click="testRandom" :disabled="status.isFlipping" class="btn btn-test">
          🎲 Test Random
        </button>
      </div>

      <div class="sound-controls">
        <h4>Sound Test:</h4>
        <button @click="playWinSound" class="btn btn-sound">🎉 Win Sound</button>
        <button @click="playLoseSound" class="btn btn-sound">😔 Lose Sound</button>
      </div>
    </div>

    <!-- ประวัติการเล่น -->
    <div class="history" v-if="gameHistory.length > 0">
      <h4>Game History:</h4>
      <div class="history-list">
        <div 
          v-for="(game, index) in gameHistory.slice(-5)" 
          :key="index"
          class="history-item"
          :class="{ win: game.won, lose: !game.won }"
        >
          <span>{{ game.choice }} vs {{ game.result }}</span>
          <span>{{ game.won ? '+' : '-' }}${{ game.amount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// Import CoinFlipper module
import { CoinFlipper } from '../src/coin-flipper.js'

export default {
  name: 'CoinFlipperDemo',
  data() {
    return {
      coinFlipper: null,
      status: {
        isInitialized: false,
        hasAudio: false,
        hasRenderer: false,
        isFlipping: false,
        isIdle: false
      },
      lastResult: null,
      balance: 1000,
      betAmount: 50,
      betChoice: 'heads',
      gameHistory: []
    }
  },
  computed: {
    statusText() {
      if (!this.status.isInitialized) return 'Initializing...'
      if (this.status.isFlipping) return 'Flipping coin...'
      if (this.status.isIdle) return 'Idle (spinning)'
      return 'Ready'
    }
  },
  async mounted() {
    try {
      // สร้าง CoinFlipper instance
      this.coinFlipper = new CoinFlipper('coinCanvas', {
        idleSpeed: 0.02,
        flipDuration: 2000,
        enableSound: true
      })

      // รอให้พร้อมใช้งาน
      await this.coinFlipper.ready()
      
      // อัพเดทสถานะ
      this.updateStatus()
      
      // เริ่ม idle animation
      await this.coinFlipper.startIdle()
      this.updateStatus()

      // ตั้ง interval สำหรับอัพเดทสถานะ
      this.statusInterval = setInterval(() => {
        this.updateStatus()
      }, 100)

    } catch (error) {
      console.error('Failed to initialize CoinFlipper:', error)
      alert('Failed to initialize CoinFlipper. Please check console for details.')
    }
  },
  beforeUnmount() {
    // ทำความสะอาด
    if (this.statusInterval) {
      clearInterval(this.statusInterval)
    }
    if (this.coinFlipper) {
      this.coinFlipper.destroy()
    }
  },
  methods: {
    updateStatus() {
      if (this.coinFlipper) {
        this.status = { ...this.coinFlipper.status }
      }
    },

    async startIdle() {
      try {
        await this.coinFlipper.startIdle()
        this.updateStatus()
      } catch (error) {
        console.error('Failed to start idle:', error)
      }
    },

    async stopIdle() {
      try {
        await this.coinFlipper.stopIdle()
        this.updateStatus()
      } catch (error) {
        console.error('Failed to stop idle:', error)
      }
    },

    async flipCoin() {
      if (this.betAmount > this.balance || this.betAmount < 1) return

      try {
        // หยุด idle ก่อนทอย
        await this.coinFlipper.stopIdle()
        
        // ทอยเหรียญ
        const result = await this.coinFlipper.toss()
        this.lastResult = result
        
        // ตรวจสอบผลลัพธ์
        const won = result === this.betChoice
        
        // อัพเดทเงิน
        if (won) {
          this.balance += this.betAmount
          await this.coinFlipper.playWinSound()
        } else {
          this.balance -= this.betAmount
          await this.coinFlipper.playLoseSound()
        }

        // บันทึกประวัติ
        this.gameHistory.push({
          choice: this.betChoice,
          result: result,
          amount: this.betAmount,
          won: won,
          timestamp: new Date()
        })

        // กลับไป idle หลังจากทอยเสร็จ
        setTimeout(async () => {
          await this.coinFlipper.startIdle()
          this.updateStatus()
        }, 1000)

      } catch (error) {
        console.error('Failed to flip coin:', error)
      }
    },

    async testHeads() {
      try {
        await this.coinFlipper.stopIdle()
        const result = await this.coinFlipper.toss('heads')
        this.lastResult = result
        setTimeout(async () => {
          await this.coinFlipper.startIdle()
        }, 1000)
      } catch (error) {
        console.error('Test failed:', error)
      }
    },

    async testTails() {
      try {
        await this.coinFlipper.stopIdle()
        const result = await this.coinFlipper.toss('tails')
        this.lastResult = result
        setTimeout(async () => {
          await this.coinFlipper.startIdle()
        }, 1000)
      } catch (error) {
        console.error('Test failed:', error)
      }
    },

    async testRandom() {
      try {
        await this.coinFlipper.stopIdle()
        const result = await this.coinFlipper.toss()
        this.lastResult = result
        setTimeout(async () => {
          await this.coinFlipper.startIdle()
        }, 1000)
      } catch (error) {
        console.error('Test failed:', error)
      }
    },

    async playWinSound() {
      try {
        await this.coinFlipper.playWinSound()
      } catch (error) {
        console.error('Failed to play win sound:', error)
      }
    },

    async playLoseSound() {
      try {
        await this.coinFlipper.playLoseSound()
      } catch (error) {
        console.error('Failed to play lose sound:', error)
      }
    }
  }
}
</script>

<style scoped>
.coin-flipper-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
}

.coin-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.coin-canvas {
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.status-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #007bff;
}

.status-info p {
  margin: 5px 0;
  font-weight: 500;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.idle-controls, .betting-controls, .test-controls, .sound-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.betting-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
}

.bet-amount, .bet-choice {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bet-amount input, .bet-choice select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin: 5px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-test {
  background: #28a745;
  color: white;
}

.btn-test:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-sound {
  background: #ffc107;
  color: #212529;
}

.btn-sound:hover:not(:disabled) {
  background: #e0a800;
}

.history {
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history h4 {
  margin-top: 0;
  color: #333;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.history-item.win {
  background: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.history-item.lose {
  background: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.test-controls h4, .sound-controls h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
  font-size: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .coin-flipper-demo {
    padding: 10px;
  }

  .coin-canvas {
    width: 300px;
    height: 300px;
  }

  .betting-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .btn {
    width: 100%;
    margin: 5px 0;
  }
}
</style>
