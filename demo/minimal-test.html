<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal CoinFlipper Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        canvas {
            border: 2px solid #333;
            border-radius: 8px;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        .info {
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🪙 Minimal CoinFlipper Test</h1>
    
    <div class="info" id="info">Loading...</div>
    
    <canvas id="coinCanvas" width="400" height="400"></canvas>
    
    <div style="text-align: center;">
        <button onclick="testIdle()">Test Idle</button>
        <button onclick="testFlip()">Test Flip</button>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- CoinFlipper Module -->
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        const infoEl = document.getElementById('info');
        
        function updateInfo(message) {
            infoEl.textContent = message;
            console.log(message);
        }
        
        async function init() {
            try {
                updateInfo('Checking Three.js...');
                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js not loaded');
                }
                updateInfo('Three.js OK ✓');
                
                updateInfo('Checking CoinFlipper...');
                if (typeof CoinFlipper === 'undefined') {
                    throw new Error('CoinFlipper not available');
                }
                updateInfo('CoinFlipper OK ✓');
                
                updateInfo('Creating CoinFlipper instance...');
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.05,
                    flipDuration: 2000,
                    enableSound: true
                });
                
                updateInfo('Waiting for ready...');
                await coinFlipper.ready();
                
                updateInfo('Starting idle animation...');
                await coinFlipper.startIdle();
                
                updateInfo('Ready! You should see a spinning coin ✓');
                
            } catch (error) {
                updateInfo(`Error: ${error.message}`);
                console.error('Full error:', error);
            }
        }
        
        async function testIdle() {
            if (!coinFlipper) return;
            try {
                await coinFlipper.startIdle();
                updateInfo('Idle animation started');
            } catch (error) {
                updateInfo(`Idle error: ${error.message}`);
            }
        }
        
        async function testFlip() {
            if (!coinFlipper) return;
            try {
                updateInfo('Flipping...');
                const result = await coinFlipper.toss();
                updateInfo(`Flip result: ${result}`);
            } catch (error) {
                updateInfo(`Flip error: ${error.message}`);
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>
