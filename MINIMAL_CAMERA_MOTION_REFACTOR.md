# Enhanced Camera Motion Refactor - Flipping State with Pitch Tracking

## Overview
The Flipping State camera system has been enhanced to implement **intelligent pitch tracking** that follows the coin's trajectory with natural camera movements. The camera now tilts up when the coin is high and tilts down when it falls, creating a more immersive and natural viewing experience while maintaining comfort and preventing motion sickness.

## Key Design Principles

### 🎯 **Enhanced Goals**: Natural pitch tracking with minimal camera movement
- **Position Tracking**: Camera moves subtly (15% of coin movement)
- **Pitch Tracking**: Camera tilts naturally to follow coin trajectory
- **Dolly Movement**: Camera moves closer during flip for better engagement
- **Smooth Transitions**: All movements use sophisticated easing
- **Motion Comfort**: Limited ranges prevent motion sickness
- **Visual References**: Ground plane always visible for spatial orientation

## Implementation Details

### 1. Enhanced Reference Point Storage
```javascript
flipping: {
    basePosition: new THREE.Vector3(0, 0.2, 2.5), // เก็บตำแหน่งเริ่มต้น
    currentPosition: new THREE.Vector3(0, 0.2, 2.5),
    baseLookAt: new THREE.Vector3(0, -0.5, 0), // เก็บ lookAt เริ่มต้น
    currentLookAt: new THREE.Vector3(0, -0.5, 0),

    // เพิ่มการควบคุมมุม pitch
    basePitch: 0, // มุม pitch เริ่มต้น (คำนวณจาก baseLookAt)
    currentPitch: 0, // มุม pitch ปัจจุบัน
    maxPitchRange: 15 * Math.PI / 180, // ช่วงมุม pitch สูงสุด ±15°
    pitchSmoothingFactor: 0.06, // ความนุ่มนวลของการเปลี่ยนมุม pitch

    // การ dolly in และยกตำแหน่ง
    dollyAmount: 0.3, // ระยะ dolly in (เข้าใกล้เหรียญ)
    liftAmount: 0.2 // ระยะยกกล้องขึ้น
}
```

**Purpose**: Store comprehensive camera state including pitch angles and movement parameters for natural coin tracking.

### 2. Enhanced Movement Tracking with Pitch Control
```javascript
// === 1. การเคลื่อนตำแหน่งกล้อง (Position Tracking) ===
const coinMovementY = this.coinPositionY; // การเคลื่อนจริงของเหรียญ
const cameraMovementY = coinMovementY * flippingPos.trackingRatio; // กล้องเคลื่อนแค่ 15%

// คำนวณตำแหน่งเป้าหมายของกล้อง (รวม dolly in และ lift)
const targetPosition = flippingPos.basePosition.clone();
targetPosition.y += cameraMovementY + (flippingPos.liftAmount * easingFactor);
targetPosition.z -= dollyProgress; // เข้าใกล้เหรียญ

// === 2. การปรับมุม Pitch ตามตำแหน่งเหรียญ ===
const cameraToTarget = new THREE.Vector3().subVectors(coinWorldPosition, targetPosition);
const distance = new THREE.Vector3(cameraToTarget.x, 0, cameraToTarget.z).length();
const desiredPitch = Math.atan2(-cameraToTarget.y, distance);
```

**Enhanced Features**:
- **Position Tracking**: 15% of coin movement (same as before)
- **Pitch Tracking**: Camera tilts ±15° to follow coin trajectory
- **Dolly Movement**: Camera moves 0.3 units closer during flip
- **Lift Movement**: Camera rises 0.2 units for better angle
- **World Space Calculation**: Real-time coin position tracking

### 3. Enhanced Pitch Control System
```javascript
// จำกัดมุม pitch ไม่ให้เกิน ±15° จากมุมเริ่มต้น
const maxPitch = flippingPos.basePitch + flippingPos.maxPitchRange;
const minPitch = flippingPos.basePitch - flippingPos.maxPitchRange;
const clampedPitch = Math.max(minPitch, Math.min(maxPitch, desiredPitch));

// อัพเดทมุม pitch แบบ smooth
const pitchDiff = clampedPitch - flippingPos.currentPitch;
flippingPos.currentPitch += pitchDiff * flippingPos.pitchSmoothingFactor * easingFactor;
```

**Enhanced Limitations**:
- **Pitch Range**: ±15° from base angle (increased for better tracking)
- **Smooth Interpolation**: Gradual pitch changes with dedicated smoothing factor
- **Clamping**: Hard limits prevent excessive camera tilting
- **No Roll**: Camera never tilts left/right (prevents disorientation)
- **Natural LookAt**: Calculated from pitch angle for realistic camera behavior

### 4. Advanced Easing System
```javascript
createFlipEasing(t) {
    if (t < 0.3) {
        return 2 * t * t; // Ease-in: ค่อยๆ เร่ง
    } else if (t < 0.7) {
        return 1; // Peak: เคลื่อนเร็วสุด
    } else {
        const adjustedT = (t - 0.7) / 0.3;
        return 1 - 0.5 * adjustedT * adjustedT; // Ease-out: ค่อยๆ ช้าลง
    }
}
```

**Easing Phases**:
1. **Start (0-30%)**: Gradual acceleration
2. **Peak (30-70%)**: Maximum tracking speed
3. **End (70-100%)**: Gradual deceleration preparing for result state

### 5. Enhanced Visual References
```javascript
// Larger, more visible ground plane
const groundGeometry = new THREE.PlaneGeometry(15, 15);

// Enhanced grid visibility
ctx.strokeStyle = '#666666';
ctx.globalAlpha = 0.5; // More visible grid lines
```

**Reference Improvements**:
- **Larger Ground**: 15x15 units (was 10x10)
- **Clearer Grid**: More visible reference lines
- **Higher Opacity**: 70% ground opacity (was 50%)
- **Always Visible**: Ground stays in frame during all camera movements

## Enhanced Pitch Tracking Workflow

### 🎯 **Natural Camera Behavior**:
1. **เมื่อเหรียญถูกโยนขึ้น** → กล้องแหงนขึ้นเล็กน้อย (tilt up)
2. **ขณะเหรียญอยู่สูง** → กล้องมองขึ้นตามเหรียญ
3. **พอเหรียญตกลง** → กล้องค่อยๆ ลดมุมมองกลับ
4. **การเคลื่อนที่พร้อมกัน** → dolly in และยกตำแหน่งกล้องขึ้นเล็กน้อย

### 📐 **Pitch Calculation Process**:
```javascript
// 1. คำนวณตำแหน่งของเหรียญใน world space ทุกเฟรม
const coinWorldPosition = new THREE.Vector3(0, this.coinPositionY, 0);

// 2. คำนวณมุม pitch ที่ต้องการเพื่อมองไปที่เหรียญ
const cameraToTarget = new THREE.Vector3().subVectors(coinWorldPosition, targetPosition);
const distance = new THREE.Vector3(cameraToTarget.x, 0, cameraToTarget.z).length();
const desiredPitch = Math.atan2(-cameraToTarget.y, distance);

// 3. จำกัดมุม pitch และใช้ smooth interpolation
const clampedPitch = Math.max(minPitch, Math.min(maxPitch, desiredPitch));
flippingPos.currentPitch += pitchDiff * flippingPos.pitchSmoothingFactor * easingFactor;
```

## Camera Movement Flow

### Phase 1: Enhanced Initialization
```javascript
transitionCameraToFlippingState() {
    // Store current position as base reference
    flippingPos.basePosition.copy(this.camera.position);
    flippingPos.baseLookAt.copy(new THREE.Vector3(0, -0.5, 0));

    // คำนวณมุม pitch เริ่มต้นจาก baseLookAt และ basePosition
    const direction = new THREE.Vector3().subVectors(flippingPos.baseLookAt, flippingPos.basePosition);
    direction.normalize();
    flippingPos.basePitch = Math.asin(-direction.y); // มุม pitch เริ่มต้น
    flippingPos.currentPitch = flippingPos.basePitch;

    flippingPos.flipStartTime = Date.now();
}
```

### Phase 2: Enhanced Tracking with Pitch
```javascript
updateFlippingCameraTracking() {
    // Calculate coin position in world space every frame
    const coinWorldPosition = new THREE.Vector3(0, this.coinPositionY, 0);

    // Calculate limited movement (15% of coin movement) + dolly + lift
    const cameraMovementY = coinMovementY * flippingPos.trackingRatio;
    targetPosition.y += cameraMovementY + (flippingPos.liftAmount * easingFactor);
    targetPosition.z -= dollyProgress; // Move closer to coin

    // Calculate desired pitch to look at coin
    const desiredPitch = Math.atan2(-cameraToTarget.y, distance);
    const clampedPitch = Math.max(minPitch, Math.min(maxPitch, desiredPitch));

    // Apply smooth interpolation for both position and pitch
    flippingPos.currentPosition.lerp(targetPosition, smoothingFactor * easingFactor);
    flippingPos.currentPitch += pitchDiff * pitchSmoothingFactor * easingFactor;

    // Apply calculated values to camera
    this.camera.position.copy(flippingPos.currentPosition);
    this.camera.lookAt(calculatedLookAtFromPitch);
}
```

### Phase 3: Result Transition
```javascript
transitionCameraToResultState() {
    // Use current flipping position as start point
    // Apply ease-out for gentle transition to top-down view
    const easedProgress = this.easeOutCubic(progress);
}
```

## Motion Sickness Prevention Features

### ✅ **Implemented Safeguards**:

1. **Limited Movement Range**: Camera never moves more than 20% of coin movement
2. **No Sudden Direction Changes**: Smooth easing prevents jarring movements
3. **Stable Reference Points**: Ground grid always visible for spatial orientation
4. **Constrained Rotation**: Maximum 12° pitch change prevents disorientation
5. **No Roll Motion**: Camera never tilts sideways
6. **Gradual Transitions**: All state changes use smooth easing functions

### 📊 **Enhanced Movement Statistics**:
- **Coin Movement Range**: ±2.5 units vertically
- **Camera Position Movement**: ±0.375 units vertically (15% of coin)
- **Camera Pitch Range**: ±15° from base angle (enhanced from 12°)
- **Dolly Movement**: 0.3 units closer to coin during flip
- **Lift Movement**: 0.2 units upward during flip
- **Pitch Smoothing**: 6% interpolation factor (slower than position)
- **Transition Duration**: 1200ms for result state (extended for comfort)

## Performance Optimizations

### Debug Logging Control
```javascript
if (elapsed % 500 < 16) {
    console.log('📷 Flipping camera tracking:', {
        coinY: coinMovementY.toFixed(3),
        cameraY: cameraMovementY.toFixed(3),
        trackingRatio: (flippingPos.trackingRatio * 100).toFixed(1) + '%'
    });
}
```

**Benefits**:
- Reduced console spam (every 500ms instead of every frame)
- Useful debugging information when needed
- Performance-friendly logging

## Configuration Options

### Enhanced Adjustable Parameters
```javascript
trackingRatio: 0.15,            // 15% position tracking (adjustable 0.1-0.2)
maxPitchRange: 15°,             // Maximum pitch range ±15° (adjustable 10-20°)
smoothingFactor: 0.08,          // Position smoothing (adjustable 0.05-0.15)
pitchSmoothingFactor: 0.06,     // Pitch smoothing (adjustable 0.04-0.1)
dollyAmount: 0.3,               // Dolly distance (adjustable 0.2-0.5)
liftAmount: 0.2,                // Lift distance (adjustable 0.1-0.3)
```

### Easy Customization Options
- **More Conservative Motion**:
  - Reduce trackingRatio to 0.1 (10%)
  - Reduce maxPitchRange to 10°
  - Reduce dollyAmount to 0.2
- **More Dynamic Motion**:
  - Increase trackingRatio to 0.2 (20%)
  - Increase maxPitchRange to 20°
  - Increase dollyAmount to 0.5
- **Smoother Transitions**:
  - Reduce smoothingFactor to 0.05
  - Reduce pitchSmoothingFactor to 0.04
- **More Responsive Tracking**:
  - Increase smoothingFactor to 0.12
  - Increase pitchSmoothingFactor to 0.08

## Testing Results

### ✅ **Enhanced Achieved Goals**:
1. **Natural Camera Behavior**: Camera tilts naturally to follow coin trajectory
2. **Reduced Motion Sickness**: Limited movement ranges prevent disorientation
3. **Enhanced Engagement**: Dolly and lift movements create more immersive experience
4. **Maintained Dynamism**: Coin appears dramatic with natural camera following
5. **Clear Visibility**: Coin remains clearly visible with optimal viewing angles
6. **Smooth Transitions**: Sophisticated easing prevents jarring movements
7. **Stable References**: Ground grid provides consistent spatial orientation
8. **Professional Feel**: Cinematic camera movements enhance production value

### 🎮 **Enhanced User Experience**:
- **Natural Viewing**: Camera behaves like human eye following a thrown object
- **Comfortable Motion**: All movements within safe ranges for motion sensitivity
- **Immersive Tracking**: Dolly and pitch movements create engagement without discomfort
- **Clear Coin Tracking**: Enhanced visibility through intelligent camera positioning
- **Smooth State Transitions**: Seamless flow between idle, flipping, and result states
- **Professional Quality**: Cinematic camera work suitable for production use
- **Universal Accessibility**: Safe for all users including those sensitive to motion

### 🏆 **Technical Achievements**:
- **Real-time Pitch Calculation**: Frame-by-frame coin position tracking
- **Constrained Movement**: Hard limits prevent excessive camera motion
- **Multi-axis Tracking**: Position, pitch, dolly, and lift work together seamlessly
- **Sophisticated Easing**: Different smoothing factors for different movement types
- **Performance Optimized**: Efficient calculations with controlled debug logging

This enhanced refactor creates a truly cinematic coin flip experience that feels natural and engaging while maintaining the highest standards of viewing comfort and accessibility.
