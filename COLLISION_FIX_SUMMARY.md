# Coin Collision Detection Fix

## Problem Identified

The coin was sinking into or clipping through the ground during the animation because the collision detection was checking if the **center** of the coin reached the ground level (Y = -1.5), rather than checking if the **bottom edge** of the coin reached the ground.

### Technical Details

- **Coin Geometry**: Cylinder with radius 0.8 and height 0.1
- **Ground Position**: Y = -1.5
- **Issue**: Collision detection was checking `coinPositionY <= -1.5`
- **Problem**: The coin's center could be at Y = -1.5 while the bottom edge was at Y = -1.55, causing the coin to appear to sink into the ground

## Solution Implemented

### 1. Added Physics Constants

Added proper constants for collision detection calculations:

```javascript
// === ค่าคงที่สำหรับ Collision Detection ===
this.coinRadius = 0.8;              // รัศมีของเหรียญ
this.coinHeight = 0.1;              // ความสูงของเหรียญ
this.groundY = -1.5;                // ตำแหน่ง Y ของพื้น
this.coinHalfHeight = this.coinHeight / 2;  // ครึ่งความสูงของเหรียญ (0.05)
```

### 2. Updated Collision Detection Logic

Modified the collision detection to check the coin's bottom edge instead of its center:

```javascript
// คำนวณตำแหน่งขอบล่างของเหรียญ (coin bottom edge)
const coinBottomY = this.coinPositionY - this.coinHalfHeight;

// ตรวจสอบว่าขอบล่างของเหรียญกระทบพื้น และกำลังตกลงมา (velocity.y < 0)
if (coinBottomY <= this.groundY && this.velocity.y < 0) {
    // ตั้งตำแหน่งเหรียญให้ขอบล่างอยู่บนพื้นพอดี
    // ตำแหน่งจุดศูนย์กลางของเหรียญ = ตำแหน่งพื้น + ครึ่งความสูงของเหรียญ
    this.coinPositionY = this.groundY + this.coinHalfHeight;
    
    // ... rest of bounce logic
}
```

### 3. Improved Position Correction

When a collision is detected:
- Calculate the correct center position: `groundY + coinHalfHeight`
- This ensures the coin's bottom edge sits exactly on the ground surface
- Prevents any visual clipping or sinking

### 4. Enhanced Final Position Setting

Added explicit position setting when bouncing stops:

```javascript
if (this.bounceCount >= this.maxBounces && Math.abs(this.velocity.y) < 0.05) {
    this.velocity.y = 0;
    // ตั้งตำแหน่งสุดท้ายให้แน่นอน
    this.coinPositionY = this.groundY + this.coinHalfHeight;
    this.stopFlipping();
}
```

## Results

### Before Fix
- Coin center at Y = -1.5 meant coin bottom at Y = -1.55
- Coin appeared to sink 0.05 units into the ground
- Unrealistic collision behavior

### After Fix
- Coin bottom edge stops exactly at Y = -1.5 (ground level)
- Coin center positioned at Y = -1.45 (ground + half height)
- Realistic collision and bouncing behavior
- No visual clipping through the ground

## Testing

Created `collision-test.html` with:
- Debug information showing coin positions
- Multiple flip tests
- Real-time collision data monitoring
- Visual verification of proper ground collision

The fix ensures the coin behaves realistically when hitting the ground surface, with proper physics simulation and no visual artifacts.
