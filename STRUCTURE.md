# 📁 CoinFlipper Module Structure

โครงสร้างไฟล์และ folder ของ CoinFlipper Module

## 🗂️ Folder Structure

```
coin-flipper-module/
├── 📁 src/                          # ไฟล์โมดูลหลัก
│   ├── 📄 coin-flipper.js           # โมดูลหลัก (JavaScript)
│   └── 📄 coin-flipper.d.ts         # TypeScript definitions
├── 📁 examples/                     # ตัวอย่างการใช้งาน
│   ├── 📄 vue-example.vue           # Vue.js (JavaScript)
│   └── 📄 vue-typescript-example.vue # Vue.js (TypeScript)
├── 📁 demo/                         # Demo และการทดสอบ
│   └── 📄 coin-flipper-demo.html    # HTML Demo
├── 📁 docs/                         # เอกสารประกอบ
│   └── 📄 COIN_FLIPPER_DOCS.md      # เอกสารการใช้งานแบบละเอียด
├── 📄 index.js                      # Entry point สำหรับ easy import
├── 📄 package.json                  # Package configuration
├── 📄 README.md                     # เอกสารหลัก
├── 📄 INSTALLATION.md               # คู่มือการติดตั้ง
├── 📄 CHANGELOG.md                  # บันทึกการเปลี่ยนแปลง
├── 📄 LICENSE                       # ใบอนุญาต MIT
├── 📄 .gitignore                    # Git ignore rules
└── 📄 STRUCTURE.md                  # เอกสารนี้
```

## 📄 File Descriptions

### 🔧 Core Files

#### `src/coin-flipper.js`
- โมดูลหลักที่รวม Three.js, AudioManager, และ CoinRenderer
- ประกอบด้วย 3 classes หลัก:
  - `CoinFlipper`: คลาสหลักสำหรับการใช้งาน
  - `AudioManager`: จัดการเสียงทั้งหมด
  - `CoinRenderer`: จัดการ 3D animation
- รองรับการโหลด Three.js อัตโนมัติ
- Export ทั้ง CommonJS, AMD, และ Browser global

#### `src/coin-flipper.d.ts`
- TypeScript definitions สำหรับ type safety
- ครอบคลุม interfaces, types, และ method signatures
- รองรับ Vue.js integration
- มี global declarations สำหรับ browser usage

#### `index.js`
- Entry point สำหรับ easy import
- Re-export ทุกอย่างจาก main module
- Default export เป็น CoinFlipper class

### 📚 Examples

#### `examples/vue-example.vue`
- ตัวอย่าง Vue.js component แบบ JavaScript
- ใช้ Composition API
- แสดงการใช้งานแบบเต็มรูปแบบ
- มี betting system และ game history
- รวม CSS styling

#### `examples/vue-typescript-example.vue`
- ตัวอย่าง Vue.js component แบบ TypeScript
- ใช้ Composition API กับ TypeScript
- Type-safe implementation
- แสดงการใช้งาน types และ interfaces

### 🎮 Demo

#### `demo/coin-flipper-demo.html`
- HTML demo page สำหรับทดสอบ
- ไม่ต้องใช้ Vue.js framework
- แสดงฟีเจอร์ทั้งหมดของโมดูล
- มี UI ที่สวยงามและ responsive
- เหมาะสำหรับการทดสอบและ showcase

### 📖 Documentation

#### `docs/COIN_FLIPPER_DOCS.md`
- เอกสารการใช้งานแบบละเอียด
- API reference ครบถ้วน
- ตัวอย่างการใช้งานหลากหลาย
- Best practices และ troubleshooting
- Browser support information

#### `README.md`
- เอกสารหลักของโมดูล
- Quick start guide
- Feature overview
- Basic usage examples

#### `INSTALLATION.md`
- คู่มือการติดตั้งแบบละเอียด
- วิธีการ setup ใน Vue.js projects
- Configuration สำหรับ build tools
- Troubleshooting guide

#### `CHANGELOG.md`
- บันทึกการเปลี่ยนแปลงในแต่ละ version
- Feature additions และ bug fixes
- Breaking changes
- Future plans

#### `STRUCTURE.md`
- เอกสารนี้
- อธิบายโครงสร้างไฟล์
- วัตถุประสงค์ของแต่ละไฟล์

### ⚙️ Configuration Files

#### `package.json`
- Package configuration
- Dependencies และ peerDependencies
- Scripts สำหรับ development
- Metadata และ keywords

#### `.gitignore`
- Git ignore rules
- ไฟล์ที่ไม่ต้อง commit
- Node.js และ IDE specific files

#### `LICENSE`
- MIT License
- Terms และ conditions
- Copyright information

## 🎯 Usage Patterns

### Import Patterns

```javascript
// วิธีที่ 1: Import จาก main module
import { CoinFlipper } from './coin-flipper-module/src/coin-flipper.js'

// วิธีที่ 2: Import จาก index.js
import { CoinFlipper } from './coin-flipper-module'
import CoinFlipper from './coin-flipper-module' // default export

// วิธีที่ 3: Import specific classes
import { CoinFlipper, AudioManager, CoinRenderer } from './coin-flipper-module'
```

### File Dependencies

```
coin-flipper.js
├── Three.js (auto-loaded from CDN)
├── Web Audio API (browser native)
└── Canvas API (browser native)

vue-example.vue
├── coin-flipper.js
└── Vue.js 3

vue-typescript-example.vue
├── coin-flipper.js
├── coin-flipper.d.ts
└── Vue.js 3 + TypeScript

coin-flipper-demo.html
├── coin-flipper.js
└── Three.js CDN
```

## 🔄 Development Workflow

### Adding New Features
1. แก้ไข `src/coin-flipper.js`
2. อัพเดท `src/coin-flipper.d.ts`
3. เพิ่มตัวอย่างใน `examples/`
4. อัพเดท `docs/COIN_FLIPPER_DOCS.md`
5. ทดสอบด้วย `demo/coin-flipper-demo.html`
6. อัพเดท `CHANGELOG.md`

### Testing
1. เปิด `demo/coin-flipper-demo.html` ในเบราว์เซอร์
2. ทดสอบฟีเจอร์ทั้งหมด
3. ตรวจสอบ console สำหรับ errors
4. ทดสอบใน Vue.js project จริง

### Distribution
1. ตรวจสอบ `package.json` version
2. อัพเดท `CHANGELOG.md`
3. ทดสอบทุกไฟล์
4. สร้าง release package

## 📦 Package Contents

เมื่อแจกจ่ายโมดูลนี้ จะประกอบด้วย:

### Essential Files (จำเป็น)
- `src/coin-flipper.js` - โมดูลหลัก
- `src/coin-flipper.d.ts` - TypeScript support
- `index.js` - Entry point
- `README.md` - เอกสารหลัก
- `LICENSE` - ใบอนุญาต

### Optional Files (เสริม)
- `examples/` - ตัวอย่างการใช้งาน
- `demo/` - HTML demo
- `docs/` - เอกสารละเอียด
- `INSTALLATION.md` - คู่มือติดตั้ง
- `CHANGELOG.md` - บันทึกการเปลี่ยนแปลง

## 🎯 Design Principles

1. **Self-contained**: รวม dependencies ไว้ในโมดูล
2. **Easy to use**: API ที่เรียบง่ายและชัดเจน
3. **TypeScript ready**: มี type definitions ครบถ้วน
4. **Vue.js optimized**: ออกแบบมาสำหรับ Vue.js
5. **Well documented**: เอกสารครบถ้วนและตัวอย่างชัดเจน
6. **Performance focused**: จัดการ memory และ resources อย่างมีประสิทธิภาพ

## 🔮 Future Structure

เมื่อโมดูลขยายตัวในอนาคต อาจจะมีโครงสร้างเพิ่มเติม:

```
coin-flipper-module/
├── src/
│   ├── core/           # Core functionality
│   ├── renderers/      # Different renderers
│   ├── audio/          # Audio systems
│   └── utils/          # Utility functions
├── tests/              # Unit tests
├── build/              # Build scripts
└── dist/               # Distribution files
```
