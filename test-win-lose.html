<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Win/Lose Scene Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 25px; border-radius: 10px; max-width: 800px; margin: 0 auto; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #2c3e50; margin-bottom: 10px; }
        .coin-container { display: flex; justify-content: center; margin: 25px 0; padding: 25px; background: linear-gradient(135deg, #2c3e50, #34495e); border-radius: 10px; box-shadow: inset 0 2px 10px rgba(0,0,0,0.3); }
        .coin-canvas { border: 2px solid #ecf0f1; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .controls { text-align: center; margin: 25px 0; }
        .btn { padding: 15px 30px; margin: 10px; border: none; border-radius: 8px; color: white; cursor: pointer; font-size: 16px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0,0,0,0.3); }
        .btn:disabled { background: #bdc3c7; cursor: not-allowed; transform: none; box-shadow: none; }
        .btn.idle { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }
        .btn.win { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .btn.lose { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .result { background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; font-size: 18px; font-weight: bold; border: 2px solid #bdc3c7; }
        .result.success { background: linear-gradient(135deg, #d5f4e6, #c8e6c9); color: #27ae60; border-color: #27ae60; }
        .result.error { background: linear-gradient(135deg, #fadbd8, #f5b7b1); color: #e74c3c; border-color: #e74c3c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎊 Win/Lose Scene Test</h1>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="400" height="400" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="idleBtn" class="btn idle">Reset to Idle</button>
        </div>

        <div class="controls">
            <h3 style="color: #2c3e50; margin: 20px 0 10px 0;">Win/Lose Scene Testing</h3>
            <button id="winBtn" class="btn win">🏆 Test WIN Scene</button>
            <button id="loseBtn" class="btn lose">❌ Test LOSE Scene</button>
        </div>

        <div class="result" id="result">Loading...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;

        function updateResult(message, isSuccess = null) {
            const resultElement = document.getElementById('result');
            resultElement.textContent = message;
            resultElement.className = 'result';
            if (isSuccess === true) {
                resultElement.className += ' success';
            } else if (isSuccess === false) {
                resultElement.className += ' error';
            }
        }

        function updateButtonStates() {
            const idleBtn = document.getElementById('idleBtn');
            const winBtn = document.getElementById('winBtn');
            const loseBtn = document.getElementById('loseBtn');

            if (!coinFlipper) {
                idleBtn.disabled = true;
                winBtn.disabled = true;
                loseBtn.disabled = true;
                return;
            }

            const status = coinFlipper.status;

            if (status.isFlipping) {
                idleBtn.disabled = true;
                winBtn.disabled = true;
                loseBtn.disabled = true;
            } else if (status.isIdle) {
                idleBtn.disabled = false;
                winBtn.disabled = false;
                loseBtn.disabled = false;
            } else {
                idleBtn.disabled = false;
                winBtn.disabled = true;
                loseBtn.disabled = true;
            }
        }

        async function init() {
            try {
                console.log('Initializing CoinFlipper...');
                updateResult('Loading...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.02,
                    flipDuration: 2000,
                    enableSound: true
                });

                await coinFlipper.ready();
                console.log('CoinFlipper ready!');

                await coinFlipper.startIdle();
                console.log('Started idle animation');
                updateResult('Ready for testing - Coin is in Idle state');

                setupEventListeners();
                updateButtonStates();

            } catch (error) {
                console.error('Error:', error.message);
                updateResult('Error: ' + error.message, false);
            }
        }

        function setupEventListeners() {
            document.getElementById('idleBtn').onclick = () => resetToIdle();
            document.getElementById('winBtn').onclick = () => testWinScene();
            document.getElementById('loseBtn').onclick = () => testLoseScene();
        }

        async function resetToIdle() {
            try {
                console.log('Resetting to idle...');
                updateResult('Resetting...');

                await coinFlipper.startIdle();
                console.log('Reset complete - back to idle state');
                updateResult('Ready for testing - Coin is in Idle state');
                updateButtonStates();

            } catch (error) {
                console.error('Reset error:', error.message);
                updateResult('Reset error: ' + error.message, false);
            }
        }

        async function testWinScene() {
            try {
                if (!coinFlipper.status.isIdle) {
                    updateResult('Cannot flip - Please click Reset to Idle first', false);
                    return;
                }

                updateButtonStates();
                updateResult('Testing WIN scene...');

                await coinFlipper.stopIdle();
                console.log('Stopped idle animation');

                // Test win scene with heads result
                const result = await coinFlipper.toss('heads', 'win');
                console.log('Flip result:', result, 'with WIN scene');

                updateResult('✅ WIN scene test completed! Result: ' + result, true);
                updateButtonStates();

            } catch (error) {
                console.error('Win test error:', error.message);
                updateResult('Win test error: ' + error.message, false);
                updateButtonStates();
            }
        }

        async function testLoseScene() {
            try {
                if (!coinFlipper.status.isIdle) {
                    updateResult('Cannot flip - Please click Reset to Idle first', false);
                    return;
                }

                updateButtonStates();
                updateResult('Testing LOSE scene...');

                await coinFlipper.stopIdle();
                console.log('Stopped idle animation');

                // Test lose scene with tails result
                const result = await coinFlipper.toss('tails', 'lose');
                console.log('Flip result:', result, 'with LOSE scene');

                updateResult('❌ LOSE scene test completed! Result: ' + result, true);
                updateButtonStates();

            } catch (error) {
                console.error('Lose test error:', error.message);
                updateResult('Lose test error: ' + error.message, false);
                updateButtonStates();
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
