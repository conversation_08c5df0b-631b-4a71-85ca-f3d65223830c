# 🪙 CoinFlipper Module Documentation

CoinFlipper เป็นโมดูลสำหรับการทอยเหรียญแบบ 3D ที่สามารถนำไปใช้กับ Vue.js ได้โดยตรง โดยรวม Three.js, AudioManager, และ CoinRenderer เข้าด้วยกันเป็นชุดเดียว

## ✨ Features

- 🎯 **Idle Animation**: เหรียญหมุนไปเรื่อยๆ ด้วยความเร็วที่กำหนดได้
- 🎲 **Toss Animation**: การแสดงเหรียญกำลังถูกทอยพร้อมเสียงและกำหนดผลลัพธ์ได้
- 🔊 **Sound Effects**: เสียงการทอย, เสียงชนะ, เสียงแพ้
- 🎨 **3D Graphics**: ใช้ Three.js สำหรับ animation ที่สมจริง
- 📱 **Responsive**: รองรับการใช้งานบนอุปกรณ์ต่างๆ
- 🔧 **TypeScript Support**: มี type definitions ครบถ้วน
- 🚀 **Easy Integration**: สามารถ import ใช้ใน Vue.js ได้ทันที

## 📦 Installation

### วิธีที่ 1: Copy Files
```bash
# คัดลอกไฟล์เหล่านี้ไปยังโปรเจคของคุณ
coin-flipper.js
coin-flipper.d.ts  # สำหรับ TypeScript
```

### วิธีที่ 2: Include ใน HTML
```html
<!-- Three.js (จำเป็น) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<!-- CoinFlipper Module -->
<script src="coin-flipper.js"></script>
```

## 🚀 Quick Start

### JavaScript Example
```javascript
// สร้าง CoinFlipper instance
const coinFlipper = new CoinFlipper('canvasId', {
    idleSpeed: 0.02,
    flipDuration: 2000,
    enableSound: true
});

// รอให้พร้อมใช้งาน
await coinFlipper.ready();

// เริ่ม idle animation
await coinFlipper.startIdle();

// ทอยเหรียญ
const result = await coinFlipper.toss(); // 'heads' หรือ 'tails'

// ทอยเหรียญแบบกำหนดผลลัพธ์
const result = await coinFlipper.toss('heads');

// เล่นเสียง
await coinFlipper.playWinSound();
await coinFlipper.playLoseSound();
```

### Vue.js Example (Composition API)
```vue
<template>
  <div>
    <canvas ref="coinCanvas" width="400" height="400"></canvas>
    <button @click="flipCoin" :disabled="isFlipping">Flip Coin</button>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { CoinFlipper } from './coin-flipper.js'

const coinCanvas = ref()
const coinFlipper = ref(null)
const isFlipping = ref(false)

onMounted(async () => {
  coinFlipper.value = new CoinFlipper(coinCanvas.value)
  await coinFlipper.value.ready()
  await coinFlipper.value.startIdle()
})

onBeforeUnmount(() => {
  if (coinFlipper.value) {
    coinFlipper.value.destroy()
  }
})

const flipCoin = async () => {
  isFlipping.value = true
  const result = await coinFlipper.value.toss()
  console.log('Result:', result)
  isFlipping.value = false
}
</script>
```

## 📚 API Reference

### CoinFlipper Class

#### Constructor
```javascript
new CoinFlipper(canvasId, options)
```

**Parameters:**
- `canvasId` (string | HTMLCanvasElement): Canvas element หรือ ID
- `options` (object): ตัวเลือกการตั้งค่า

**Options:**
```javascript
{
  autoLoadThreeJS: true,     // โหลด Three.js อัตโนมัติ
  threeJSCDN: 'https://...',  // URL ของ Three.js CDN
  idleSpeed: 0.02,           // ความเร็วการหมุนแบบ idle
  flipDuration: 2000,        // ระยะเวลาการทอย (ms)
  enableSound: true          // เปิด/ปิดเสียง
}
```

#### Methods

##### `ready(): Promise<void>`
รอให้โมดูลพร้อมใช้งาน

##### `startIdle(): Promise<void>`
เริ่ม idle animation (เหรียญหมุนเบาๆ)

##### `stopIdle(): Promise<void>`
หยุด idle animation

##### `toss(result?, playSound?): Promise<FlipResult>`
ทอยเหรียญ

**Parameters:**
- `result` (FlipResult | null): กำหนดผลลัพธ์ ('heads', 'tails', หรือ null สำหรับสุ่ม)
- `playSound` (boolean): เล่นเสียงหรือไม่ (default: true)

**Returns:** Promise ที่ resolve เป็น 'heads' หรือ 'tails'

##### `playWinSound(): Promise<void>`
เล่นเสียงชนะ

##### `playLoseSound(): Promise<void>`
เล่นเสียงแพ้

##### `resize(): Promise<void>`
ปรับขนาดตาม canvas

##### `destroy(): void`
ทำลายและทำความสะอาดทรัพยากร

#### Properties

##### `status: CoinFlipperStatus`
สถานะปัจจุบันของโมดูล

```javascript
{
  isInitialized: boolean,  // เริ่มต้นแล้วหรือไม่
  hasAudio: boolean,       // มี AudioManager หรือไม่
  hasRenderer: boolean,    // มี CoinRenderer หรือไม่
  isFlipping: boolean,     // กำลังทอยหรือไม่
  isIdle: boolean         // กำลัง idle หรือไม่
}
```

### AudioManager Class

#### Methods

##### `generateFlipSound(): void`
สร้างเสียงการทอยเหรียญ

##### `generateWinSound(): void`
สร้างเสียงชนะ

##### `generateLoseSound(): void`
สร้างเสียงแพ้

##### `resumeAudioContext(): void`
เปิดใช้งาน audio context (จำเป็นสำหรับ user interaction)

### CoinRenderer Class

#### Constructor
```javascript
new CoinRenderer(canvasId, options)
```

#### Methods

##### `startIdle(): void`
เริ่ม idle animation

##### `stopIdle(): void`
หยุด idle animation

##### `flipCoin(result?): Promise<FlipResult>`
ทอยเหรียญ

##### `destroy(): void`
ทำลาย renderer

##### `resize(): void`
ปรับขนาด

## 🎯 Usage Examples

### Basic Usage
```javascript
const coinFlipper = new CoinFlipper('myCanvas');
await coinFlipper.ready();
await coinFlipper.startIdle();

// ทอยเหรียญ
const result = await coinFlipper.toss();
console.log('Result:', result);
```

### Advanced Usage with Custom Options
```javascript
const coinFlipper = new CoinFlipper('myCanvas', {
  idleSpeed: 0.05,        // หมุนเร็วขึ้น
  flipDuration: 3000,     // ทอยนานขึ้น
  enableSound: false      // ปิดเสียง
});

await coinFlipper.ready();

// ตรวจสอบสถานะ
console.log(coinFlipper.status);

// ทอยแบบกำหนดผลลัพธ์
const headsResult = await coinFlipper.toss('heads');
const tailsResult = await coinFlipper.toss('tails');
```

### Vue.js Integration (Options API)
```javascript
export default {
  data() {
    return {
      coinFlipper: null,
      result: null
    }
  },
  async mounted() {
    this.coinFlipper = new CoinFlipper(this.$refs.canvas);
    await this.coinFlipper.ready();
    await this.coinFlipper.startIdle();
  },
  beforeUnmount() {
    if (this.coinFlipper) {
      this.coinFlipper.destroy();
    }
  },
  methods: {
    async flipCoin() {
      this.result = await this.coinFlipper.toss();
    }
  }
}
```

## 🔧 TypeScript Support

โมดูลนี้มี TypeScript definitions ครบถ้วน:

```typescript
import { CoinFlipper, FlipResult, CoinFlipperOptions } from './coin-flipper.js';

const options: CoinFlipperOptions = {
  idleSpeed: 0.02,
  flipDuration: 2000,
  enableSound: true
};

const coinFlipper = new CoinFlipper('canvas', options);
const result: FlipResult = await coinFlipper.toss();
```

## 🎨 Customization

### เปลี่ยนความเร็วการหมุน
```javascript
const coinFlipper = new CoinFlipper('canvas', {
  idleSpeed: 0.05  // หมุนเร็วขึ้น
});
```

### เปลี่ยนระยะเวลาการทอย
```javascript
const coinFlipper = new CoinFlipper('canvas', {
  flipDuration: 3000  // ทอย 3 วินาที
});
```

### ปิดเสียง
```javascript
const coinFlipper = new CoinFlipper('canvas', {
  enableSound: false
});
```

## 🐛 Troubleshooting

### Three.js ไม่โหลด
```javascript
// ตรวจสอบว่า Three.js โหลดแล้วหรือไม่
if (!window.THREE) {
  console.error('Three.js not loaded');
}

// หรือใช้ autoLoadThreeJS
const coinFlipper = new CoinFlipper('canvas', {
  autoLoadThreeJS: true
});
```

### เสียงไม่เล่น
```javascript
// เรียก resumeAudioContext หลังจาก user interaction
document.addEventListener('click', async () => {
  await coinFlipper.ready();
  // เสียงจะเล่นได้แล้ว
});
```

### Canvas ไม่แสดงผล
```javascript
// ตรวจสอบว่า canvas element มีอยู่
const canvas = document.getElementById('myCanvas');
if (!canvas) {
  console.error('Canvas not found');
}

// ตรวจสอบขนาด canvas
canvas.width = 400;
canvas.height = 400;
```

## 📱 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🎮 Complete Example

สร้างไฟล์ `index.html` สำหรับทดสอบ:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CoinFlipper Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .coin-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>🪙 CoinFlipper Demo</h1>

    <div class="coin-container">
        <canvas id="coinCanvas" width="400" height="400"></canvas>
    </div>

    <div class="controls">
        <button id="startIdle" class="btn-secondary">🔄 Start Idle</button>
        <button id="stopIdle" class="btn-secondary">⏹️ Stop Idle</button>
        <button id="flipRandom" class="btn-primary">🎲 Flip Random</button>
        <button id="flipHeads" class="btn-primary">👑 Flip Heads</button>
        <button id="flipTails" class="btn-primary">🦅 Flip Tails</button>
    </div>

    <div id="result" style="text-align: center; font-size: 18px; margin: 20px;"></div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- CoinFlipper Module -->
    <script src="coin-flipper.js"></script>

    <script>
        let coinFlipper;

        async function init() {
            try {
                coinFlipper = new CoinFlipper('coinCanvas');
                await coinFlipper.ready();
                await coinFlipper.startIdle();

                document.getElementById('result').textContent = 'Ready to flip!';

                // Event listeners
                document.getElementById('startIdle').onclick = async () => {
                    await coinFlipper.startIdle();
                };

                document.getElementById('stopIdle').onclick = async () => {
                    await coinFlipper.stopIdle();
                };

                document.getElementById('flipRandom').onclick = async () => {
                    document.getElementById('result').textContent = 'Flipping...';
                    const result = await coinFlipper.toss();
                    document.getElementById('result').textContent = `Result: ${result}`;
                };

                document.getElementById('flipHeads').onclick = async () => {
                    document.getElementById('result').textContent = 'Flipping to heads...';
                    const result = await coinFlipper.toss('heads');
                    document.getElementById('result').textContent = `Result: ${result}`;
                };

                document.getElementById('flipTails').onclick = async () => {
                    document.getElementById('result').textContent = 'Flipping to tails...';
                    const result = await coinFlipper.toss('tails');
                    document.getElementById('result').textContent = `Result: ${result}`;
                };

            } catch (error) {
                console.error('Failed to initialize:', error);
                document.getElementById('result').textContent = 'Failed to initialize. Check console.';
            }
        }

        // Initialize when page loads
        window.addEventListener('load', init);

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (coinFlipper) {
                coinFlipper.destroy();
            }
        });
    </script>
</body>
</html>
```

## 🔗 Integration with Popular Frameworks

### Nuxt.js
```javascript
// plugins/coin-flipper.client.js
import { CoinFlipper } from '~/assets/js/coin-flipper.js'

export default defineNuxtPlugin(() => {
  return {
    provide: {
      coinFlipper: CoinFlipper
    }
  }
})
```

### Vite + Vue 3
```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'

const app = createApp(App)

// Global property
app.config.globalProperties.$CoinFlipper = CoinFlipper

app.mount('#app')
```

## 🎯 Best Practices

### 1. Memory Management
```javascript
// ใช้ในคอมโพเนนต์
onBeforeUnmount(() => {
  if (coinFlipper.value) {
    coinFlipper.value.destroy() // สำคัญ: ทำความสะอาด
  }
})
```

### 2. Error Handling
```javascript
try {
  const result = await coinFlipper.toss()
  // handle success
} catch (error) {
  console.error('Flip failed:', error)
  // handle error
}
```

### 3. Performance
```javascript
// ใช้ requestAnimationFrame สำหรับ status updates
const updateStatus = () => {
  if (coinFlipper) {
    status.value = coinFlipper.status
  }
  requestAnimationFrame(updateStatus)
}
```

## 🚀 Advanced Features

### Custom Coin Textures
```javascript
// สามารถแก้ไขใน CoinRenderer.createCoin()
// เปลี่ยนรูปภาพหรือสีของเหรียญได้
```

### Custom Sound Effects
```javascript
// สามารถแก้ไขใน AudioManager
// เปลี่ยนเสียงหรือเพิ่มเสียงใหม่ได้
```

### Animation Customization
```javascript
// สามารถแก้ไขพารามิเตอร์ physics ใน CoinRenderer
const coinFlipper = new CoinFlipper('canvas', {
  // Custom options จะถูกส่งไปยัง CoinRenderer
})
```

## 📊 Performance Tips

1. **ใช้ requestAnimationFrame** สำหรับ status updates
2. **ทำความสะอาด** เมื่อไม่ใช้งาน (destroy())
3. **ปิดเสียง** หากไม่จำเป็น (enableSound: false)
4. **ใช้ canvas ขนาดเหมาะสม** (ไม่ใหญ่เกินไป)

## 📄 License

MIT License - สามารถใช้งานได้อย่างอิสระ

---

## 📞 Support

หากมีปัญหาหรือข้อสงสัย สามารถ:
1. ตรวจสอบ console สำหรับ error messages
2. ดู examples ในเอกสารนี้
3. ทดสอบด้วย basic HTML example ก่อน
