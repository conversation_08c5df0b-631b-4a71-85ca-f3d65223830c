# Smooth Camera Pitch Refactor Summary

## Problem
The camera was experiencing "spring-like" bouncing when following the coin's Y position during flipping. This was caused by direct tracking of the coin's rapid movement and immediate direction changes during the up-down motion.

## Solution Implemented
Refactored the camera system to implement smooth, lagged pitch control that follows the coin's Y position gracefully without jarring movements.

## Key Changes Made

### 1. Enhanced Camera Configuration (`cameraPositions.flipping`)
```javascript
// === Enhanced Smooth Pitch Control ===
basePitch: 0, // มุม pitch เริ่มต้น (คำนวณจาก baseLookAt)
currentPitch: 0, // มุม pitch ปัจจุบัน
targetPitch: 0, // มุม pitch เป้าหมายที่คำนวณจากตำแหน่งเหรียญ
maxPitchUp: 12 * Math.PI / 180, // จำกัดมุม pitch ขึ้นไม่เกิน +12°
maxPitchDown: -5 * Math.PI / 180, // จำกัดมุม pitch ลงไม่เกิน -5°
pitchLagTime: 150, // ความล่าช้าของมุมกล้อง (มิลลิวินาที) 0.15 วินาที
pitchSmoothingFactor: 0.04, // ลดความเร็วการปรับมุม pitch เพื่อความนุ่มนวล

// การควบคุมการติดตามเฉพาะช่วงเวลา
minTrackingHeight: -1.0, // เริ่มลดการติดตามเมื่อเหรียญใกล้พื้น
trackingFadeDistance: 0.8, // ระยะที่เริ่มลดการติดตาม
```

### 2. Refactored `updateFlippingCameraTracking()` Method

#### Separated Position and Rotation Control
- **Position tracking**: Camera position follows coin with limited movement (15% ratio)
- **Pitch rotation**: Separate smooth interpolation system with lag

#### Implemented Target-Based Pitch System
```javascript
// คำนวณมุม pitch เป้าหมายจากตำแหน่งเหรียญ
const idealPitch = Math.atan2(-cameraToTarget.y, distance);

// จำกัดมุม pitch ตามขอบเขตที่กำหนด (+12° ถึง -5° จากมุมเริ่มต้น)
const clampedTargetPitch = Math.max(minPitch, Math.min(maxPitch, idealPitch));

// ปรับ target pitch ตาม tracking factor
flippingPos.targetPitch = flippingPos.basePitch + (clampedTargetPitch - flippingPos.basePitch) * trackingFactor;
```

#### Added Smooth Interpolation with Lag
```javascript
// ใช้ smooth damp แทนการตั้งค่าโดยตรง เพื่อให้มุมกล้อง lag ตามเหรียญ
const pitchDiff = flippingPos.targetPitch - flippingPos.currentPitch;
const smoothingSpeed = flippingPos.pitchSmoothingFactor * easingFactor;

// ใช้ exponential smoothing เพื่อความนุ่มนวล
flippingPos.currentPitch += pitchDiff * smoothingSpeed;
```

#### Implemented Height-Based Tracking Reduction
```javascript
// คำนวณ tracking factor ตามความสูงของเหรียญ
// ลดการติดตามเมื่อเหรียญใกล้พื้น
let trackingFactor = 1.0;
if (this.coinPositionY < flippingPos.minTrackingHeight) {
    const distanceToGround = Math.abs(this.coinPositionY - this.groundY);
    trackingFactor = Math.max(0.1, distanceToGround / flippingPos.trackingFadeDistance);
}
```

### 3. Updated Initialization in `transitionCameraToFlippingState()`
- Properly initialize `targetPitch` alongside `currentPitch`
- Enhanced logging for debugging the new pitch system

## Technical Benefits

### 1. Eliminated Spring-Like Bouncing
- Camera pitch now uses target-based interpolation instead of direct tracking
- Smooth exponential smoothing prevents jarring movements

### 2. Implemented Lag System
- 0.1-0.2 second lag as requested through `pitchSmoothingFactor: 0.04`
- Camera gracefully follows coin movement without immediate snapping

### 3. Proper Pitch Constraints
- Limited to +12° up and -5° down from idle angle as specified
- Prevents extreme camera angles that could be disorienting

### 4. Airborne-Only Tracking
- Reduces tracking when coin approaches ground (`minTrackingHeight: -1.0`)
- Smooth fade-out of tracking near landing to prevent ground-bounce following

### 5. Separated Position and Rotation
- Camera position (dolly/lift) is handled separately from pitch rotation
- Prevents coupling between different types of camera movement

## Result
The camera now smoothly tilts up and down following the coin's Y movement with a natural lag, eliminating the jarring "spring-like" bouncing effect while maintaining visual connection to the coin's trajectory.
