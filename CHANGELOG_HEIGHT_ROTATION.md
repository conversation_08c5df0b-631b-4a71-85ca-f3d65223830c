# Changelog: Height & Rotation Speed System

## 📅 Date: 2025-08-27

## 🎯 Overview
เปลี่ยนการหมุนเหรียญจากเดิมที่ใช้ gravity และ damping ให้เห็นความสูงตอนโยนและความเร็วในการหมุนแทน แต่รักษาเรื่องไม่ให้เหรียญจมพื้นไว้

## 🔄 Changes Made

### 1. Settings Patterns Configuration
**File**: `src/coin-flipper.js` (lines 410-433)

**เก่า**:
```javascript
settingsPatterns: {
    heads: {
        count: 8,
        damping: 0.5,      // การลดพลังงานต่อการเด้ง
        gravity: -0.03,    // แรงโน้มถ่วง
        audioTiming: { ... }
    },
    tails: {
        count: 7,
        damping: 0.5,      // การลดพลังงานต่อการเด้ง
        gravity: -0.04,    // แรงโน้มถ่วง
        audioTiming: { ... }
    }
}
```

**ใหม่**:
```javascript
settingsPatterns: {
    heads: {
        count: 8,
        maxHeight: 4.0,        // ความสูงสูงสุดที่เหรียญจะขึ้นไป
        rotationSpeed: 0.9,    // ความเร็วในการหมุนขณะโยนขึ้น
        audioTiming: { ... }
    },
    tails: {
        count: 7,
        maxHeight: 3.2,        // ความสูงสูงสุดที่เหรียญจะขึ้นไป
        rotationSpeed: 0.7,    // ความเร็วในการหมุนขณะโยนขึ้น
        audioTiming: { ... }
    }
}
```

### 2. Physics Constants Update
**File**: `src/coin-flipper.js` (lines 571-574)

- อัปเดตคอมเมนต์เพื่อระบุว่าเป็นค่าเริ่มต้น
- ค่าจริงจะถูกกำหนดตาม pattern ที่เลือก

### 3. New Method: selectHeightAndRotationSpeed()
**File**: `src/coin-flipper.js` (lines 1425-1449)

เพิ่มเมธอดใหม่สำหรับเลือกความสูงและความเร็วหมุนตามผลลัพธ์:

```javascript
selectHeightAndRotationSpeed(result) {
    // ตรวจสอบว่าใช้ pattern หรือ random selection
    if (this.options.usePatterns && this.options.settingsPatterns[result]) {
        const pattern = this.options.settingsPatterns[result];
        return {
            height: pattern.maxHeight,
            rotationSpeed: pattern.rotationSpeed
        };
    }

    // ใช้ค่าเริ่มต้นถ้าไม่มี pattern
    const defaultHeight = result === 'heads' ? 3.8 : 3.2;
    const defaultRotationSpeed = result === 'heads' ? 0.85 : 0.75;

    return {
        height: defaultHeight,
        rotationSpeed: defaultRotationSpeed
    };
}
```

### 4. Updated selectBouncePattern() Method
**File**: `src/coin-flipper.js` (lines 1393-1418)

**เก่า**: ตั้งค่า `gravity` และ `bounceDamping`
**ใหม่**: ตั้งค่า `maxFlipHeight` และ `flipRotationSpeed`

### 5. Updated flipCoin() Method
**File**: `src/coin-flipper.js` (lines 2018-2033)

เพิ่มการเลือกความสูงและความเร็วหมุนในเมธอด `flipCoin()`:

```javascript
// เลือกความสูงและความเร็วหมุนตามผลลัพธ์
const heightAndSpeed = this.selectHeightAndRotationSpeed(finalResult);
this.maxFlipHeight = heightAndSpeed.height;
this.flipRotationSpeed = heightAndSpeed.rotationSpeed;
```

### 6. Documentation Update
**File**: `docs/bounce-patterns.md` (lines 126-162)

- อัปเดตคำแนะนำให้ใช้ `maxHeight` และ `rotationSpeed` แทน `gravity` และ `damping`
- เพิ่มส่วนอธิบายการเปลี่ยนแปลงจากระบบเก่า

### 7. Test File Creation
**File**: `demo/height-rotation-test.html`

สร้างไฟล์ทดสอบใหม่เพื่อทดสอบระบบความสูงและความเร็วหมุน:
- ปุ่มทดสอบแยกสำหรับ heads (สูง + เร็ว) และ tails (ต่ำ + ช้า)
- แสดงการตั้งค่าปัจจุบัน
- อธิบายการเปลี่ยนแปลงจากระบบเก่า

## 🔒 Collision Detection Preserved

ระบบป้องกันเหรียญจมพื้นยังคงทำงานตามเดิม:

1. **calculateLowestPoint()**: คำนวณจุดต่ำสุดของเหรียญโดยคำนึงถึงการหมุน
2. **handleBouncing()**: ตรวจสอบและแก้ไขตำแหน่งเมื่อเหรียญกระทบพื้น
3. **forceFlat()**: ตรวจสอบให้แน่ใจว่าเหรียญไม่จมพื้นหลังจากบังคับให้นอนราบ

## 🎮 User Experience

### ความแตกต่างที่เห็นได้:
- **หัว (Heads)**: โยนสูงขึ้น (4.0) และหมุนเร็วขึ้น (0.9)
- **ก้อย (Tails)**: โยนต่ำลง (3.2) และหมุนช้าลง (0.7)

### ประโยชน์:
1. เห็นความแตกต่างของการโยนแต่ละแบบชัดเจนขึ้น
2. ยังคงความสมจริงในการกระเด้งและการไม่จมพื้น
3. ง่ายต่อการปรับแต่งและทดสอบ

## 🧪 Testing

ใช้ `demo/height-rotation-test.html` เพื่อทดสอบ:
1. เปิดไฟล์ในเบราว์เซอร์
2. ทดสอบปุ่ม "ทอย หัว" เพื่อดูการโยนสูงและหมุนเร็ว
3. ทดสอบปุ่ม "ทอย ก้อย" เพื่อดูการโยนต่ำและหมุนช้า
4. ทดสอบปุ่ม "ทอยสุ่ม" เพื่อดูระบบเลือกอัตโนมัติ

## 🔮 Future Enhancements

1. เพิ่มการปรับแต่ง UI สำหรับ maxHeight และ rotationSpeed
2. เพิ่มเอฟเฟกต์ visual เพื่อเน้นความแตกต่างของความสูง
3. เพิ่มการบันทึกสถิติการโยนแต่ละแบบ
