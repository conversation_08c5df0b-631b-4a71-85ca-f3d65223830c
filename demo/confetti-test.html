<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎊 Epic Confetti Effect Test - Music Festival Style</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container { 
            background: rgba(255, 255, 255, 0.1); 
            padding: 25px; 
            border-radius: 15px; 
            max-width: 900px; 
            margin: 0 auto; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 { 
            text-align: center; 
            color: #fff; 
            margin-bottom: 10px; 
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .subtitle { 
            text-align: center; 
            color: rgba(255, 255, 255, 0.8); 
            margin-bottom: 30px; 
            font-size: 18px; 
        }
        .coin-container { 
            display: flex; 
            justify-content: center; 
            margin: 25px 0; 
            padding: 25px; 
            background: rgba(0, 0, 0, 0.3); 
            border-radius: 15px; 
            box-shadow: inset 0 4px 20px rgba(0,0,0,0.5);
        }
        .coin-canvas { 
            border: 3px solid rgba(255, 255, 255, 0.3); 
            border-radius: 10px; 
            box-shadow: 0 8px 16px rgba(0,0,0,0.4); 
        }
        .controls { 
            text-align: center; 
            margin: 25px 0; 
        }
        .btn { 
            padding: 18px 35px; 
            margin: 12px; 
            border: none; 
            border-radius: 12px; 
            background: linear-gradient(135deg, #ff6b6b, #ee5a24); 
            color: white; 
            cursor: pointer; 
            font-size: 18px; 
            font-weight: 700; 
            transition: all 0.3s ease; 
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn:hover { 
            transform: translateY(-3px) scale(1.05); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.4); 
        }
        .btn:disabled { 
            background: rgba(255, 255, 255, 0.2); 
            cursor: not-allowed; 
            transform: none; 
            box-shadow: none; 
        }
        .btn.win { 
            background: linear-gradient(135deg, #00d2ff, #3a7bd5); 
            animation: pulse 2s infinite;
        }
        .btn.confetti { 
            background: linear-gradient(135deg, #f093fb, #f5576c); 
            position: relative;
            overflow: hidden;
        }
        .btn.confetti::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        .result { 
            background: rgba(255, 255, 255, 0.1); 
            padding: 20px; 
            border-radius: 12px; 
            margin: 20px 0; 
            text-align: center; 
            font-size: 20px; 
            font-weight: bold; 
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
        }
        .result.success { 
            background: rgba(39, 174, 96, 0.3); 
            border-color: #27ae60; 
            color: #2ecc71;
        }
        .debug { 
            background: rgba(0, 0, 0, 0.3); 
            padding: 15px; 
            border-radius: 10px; 
            margin: 20px 0; 
            font-family: 'Courier New', monospace; 
            font-size: 12px; 
            white-space: pre-wrap; 
            max-height: 300px; 
            overflow-y: auto; 
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }
        .instructions h3 {
            margin-top: 0;
            color: #f39c12;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .feature-list li:before {
            content: "🎆 ";
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎊 Epic Confetti Effect Test</h1>
        <p class="subtitle">Music Festival Style - พุ่งจากด้านล่างเหมือนพุเวที!</p>
        
        <div class="instructions">
            <h3>🎯 New Epic Features:</h3>
            <ul class="feature-list">
                <li><strong>8 Confetti Launchers</strong> - พุ่งจากทุกมุมเหมือนพุเวที music festival</li>
                <li><strong>2000 Particles</strong> - อนุภาคมากกว่าเดิม 2 เท่า!</li>
                <li><strong>Dynamic Colors</strong> - สีสันสดใสแบบ festival พร้อมเปล่งประกาย</li>
                <li><strong>Realistic Physics</strong> - แรงโน้มถ่วง, ความต้านอากาศ, แรงลม, การเด้ง</li>
                <li><strong>Epic Sound Effects</strong> - เสียงชัยชนะ + confetti launch + victory fanfare</li>
                <li><strong>Dynamic Lighting</strong> - แสงไฟเปลี่ยนสีแบบ rainbow effect</li>
                <li><strong>Multiple Shapes</strong> - กระดาษ, วงกลม, สามเหลี่ยม หลากหลายรูปทรง</li>
            </ul>
        </div>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="500" height="500" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="winBtn" class="btn win">🏆 WIN + EPIC CONFETTI!</button>
            <button id="confettiOnlyBtn" class="btn confetti">🎊 CONFETTI ONLY</button>
            <button id="resetBtn" class="btn">🔄 RESET</button>
        </div>

        <div class="result" id="result">พร้อมทดสอบ Epic Confetti Effect!</div>
        <div class="debug" id="debug">กำลังโหลด...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugElement = document.getElementById('debug');
        let resultElement = document.getElementById('result');

        function log(message) {
            console.log(message);
            debugElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        function updateResult(message, isSuccess = null) {
            resultElement.textContent = message;
            resultElement.className = 'result';
            if (isSuccess === true) {
                resultElement.className += ' success';
            }
        }

        async function init() {
            try {
                log('🎊 เริ่มต้น Epic Confetti Test...');
                updateResult('กำลังโหลด...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.03,
                    flipDuration: 3000,
                    enableSound: true
                });

                await coinFlipper.ready();
                log('✅ CoinFlipper พร้อมใช้งาน!');

                await coinFlipper.startIdle();
                log('🪙 เริ่ม idle animation');
                updateResult('🎊 พร้อมทดสอบ Epic Confetti Effect!', true);

                setupEventListeners();

            } catch (error) {
                log('❌ ข้อผิดพลาด: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message);
            }
        }

        function setupEventListeners() {
            document.getElementById('winBtn').onclick = () => testWinWithConfetti();
            document.getElementById('confettiOnlyBtn').onclick = () => testConfettiOnly();
            document.getElementById('resetBtn').onclick = () => resetToIdle();
        }

        async function testWinWithConfetti() {
            try {
                log('\n🏆 === ทดสอบ WIN + EPIC CONFETTI ===');
                updateResult('🏆 กำลังทดสอบ WIN + Epic Confetti...');

                // ทอยเหรียญให้ชนะ
                await coinFlipper.stopIdle();
                const result = await coinFlipper.toss('heads', 'win');
                
                log(`🎊 ผลลัพธ์: ${result} - Win scene with EPIC confetti!`);
                updateResult('🎊 Epic Confetti Effect กำลังแสดง! ดูการพุ่งจากด้านล่าง!', true);

            } catch (error) {
                log('❌ ข้อผิดพลาด: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message);
            }
        }

        async function testConfettiOnly() {
            try {
                log('\n🎊 === ทดสอบ CONFETTI ONLY ===');
                updateResult('🎊 กำลังทดสอบ Confetti Effect เท่านั้น...');

                // เรียก confetti effect โดยตรง
                if (coinFlipper && coinFlipper.coinRenderer) {
                    coinFlipper.coinRenderer.createConfettiEffect();
                    log('🎆 เรียก createConfettiEffect() โดยตรง');
                    updateResult('🎆 Epic Confetti Effect แสดงแล้ว! ดูการพุ่งจากทุกมุม!', true);
                } else {
                    throw new Error('CoinFlipper ยังไม่พร้อม');
                }

            } catch (error) {
                log('❌ ข้อผิดพลาด: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message);
            }
        }

        async function resetToIdle() {
            try {
                log('🔄 รีเซ็ตกลับสู่สถานะ Idle...');
                updateResult('กำลังรีเซ็ต...');

                await coinFlipper.startIdle();
                log('✅ รีเซ็ตเสร็จสิ้น');
                updateResult('🎊 พร้อมทดสอบ Epic Confetti Effect อีกครั้ง!', true);

            } catch (error) {
                log('❌ ข้อผิดพลาดในการรีเซ็ต: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message);
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
