# Changelog: Bounce Rotation System

## 📅 Date: 2025-08-27

## 🎯 Overview
ตอนตกลงมาแล้วเด้ง ให้ทำการหมุนด้วย แต่ให้เลิกหมุนเมื่อความสูงการเด้งต่ำกว่าความสูงเหรียญ ให้ทำการแสดงหน้าผลลัพธ์เลย เพื่อป้องกันเหรียญจมพื้น

## 🔄 Changes Made

### 1. Updated updateCoinRotation() Method
**File**: `src/coin-flipper.js` (lines 1582-1616)

**เก่า**: หยุดหมุนในช่วงลงและเด้ง ใช้การหมุนแบบ deterministic
**ใหม่**: หมุนต่อไปในทุกช่วง แต่มีการตรวจสอบความสูงในช่วงเด้ง

```javascript
updateCoinRotation() {
    if (this.flipPhase === 'ascending') {
        // ช่วงขึ้น: ใช้ความเร็วหมุนที่กำหนด
        this.coinRotationX += this.flipRotationSpeed;
    } else if (this.flipPhase === 'descending') {
        // ช่วงลง: ใช้ความเร็วหมุนที่กำหนดต่อไป
        this.coinRotationX += this.flipRotationSpeed;
    } else if (this.flipPhase === 'bouncing') {
        // ช่วงเด้ง: ตรวจสอบความสูงก่อนหมุน
        const currentBounceHeight = this.coinPositionY - this.groundY;
        
        // หยุดหมุนเมื่อความสูงการเด้งต่ำกว่าความสูงของเหรียญ
        if (currentBounceHeight > this.coinHeight) {
            // ยังเด้งสูงกว่าความสูงเหรียญ - หมุนต่อไป
            this.coinRotationX += this.flipRotationSpeed * 0.8; // หมุนช้าลงเล็กน้อย
        } else {
            // เด้งต่ำกว่าความสูงเหรียญแล้ว - หยุดหมุนและแสดงผลลัพธ์
            this.forceFlat();
            return;
        }
    }
}
```

### 2. Enhanced handleBouncing() Method
**File**: `src/coin-flipper.js` (lines 1650-1688)

**เพิ่มเติม**:
- คำนวณความสูงการเด้งต่อไป (`nextBounceHeight`)
- เพิ่มการ log ข้อมูลความสูงเพื่อการ debug
- เพิ่มเงื่อนไขหยุดการเด้งเมื่อความสูงต่อไปจะต่ำกว่าความสูงเหรียญ

```javascript
// คำนวณความสูงการเด้งหลังจากการกระเด้งนี้
const nextBounceHeight = Math.abs(this.velocity.y) / Math.abs(this.gravity);
const currentBounceHeight = this.coinPositionY - this.groundY;

// หยุดการกระเด้งเมื่อ:
// 1. กระเด้งครบจำนวนเป้าหมายและความเร็วต่ำพอ หรือ
// 2. ความสูงการเด้งต่อไปจะต่ำกว่าความสูงของเหรียญ
const willBounceBelow = nextBounceHeight < this.coinHeight;
if ((this.bounceCount >= this.targetBounces && Math.abs(this.velocity.y) < 0.12) || willBounceBelow) {
    // หยุดการเด้งและแสดงผลลัพธ์
}
```

### 3. Improved Logging
**File**: `src/coin-flipper.js` (lines 1676-1688)

เพิ่มข้อมูล debug ที่ละเอียดขึ้น:
- `stopReason`: เหตุผลที่หยุดการเด้ง
- `nextBounceHeight`: ความสูงการเด้งครั้งต่อไป
- `coinHeight`: ความสูงของเหรียญ
- `willBounceBelow`: จะเด้งต่ำกว่าความสูงเหรียญหรือไม่

### 4. Test File Creation
**File**: `demo/bounce-rotation-test.html`

สร้างไฟล์ทดสอบใหม่เพื่อทดสอบระบบการหมุนในช่วงเด้ง:
- แสดงการตั้งค่าปัจจุบัน
- อธิบายระบบการทำงานใหม่
- คำแนะนำการทดสอบ

## 🔧 ระบบการทำงานใหม่

### การหมุนในแต่ละช่วง:
1. **ช่วงขึ้น (Ascending)**: หมุนด้วยความเร็ว `flipRotationSpeed`
2. **ช่วงลง (Descending)**: หมุนต่อไปด้วยความเร็วเดิม
3. **ช่วงเด้ง (Bouncing)**: หมุนด้วยความเร็ว `flipRotationSpeed * 0.8` (ช้าลงเล็กน้อย)

### เงื่อนไขหยุดหมุน:
- เมื่อความสูงปัจจุบันของเหรียญ (`coinPositionY - groundY`) ≤ ความสูงเหรียญ (`coinHeight = 0.1`)
- เรียก `forceFlat()` ทันทีเพื่อแสดงผลลัพธ์

### เงื่อนไขหยุดเด้ง (เพิ่มเติม):
- เงื่อนไขเดิม: เด้งครบจำนวนเป้าหมาย + ความเร็วต่ำ
- เงื่อนไขใหม่: ความสูงการเด้งต่อไปจะต่ำกว่าความสูงเหรียญ

## 🔒 Collision Detection Preserved

ระบบป้องกันเหรียญจมพื้นยังคงทำงานตามเดิม:
1. **calculateLowestPoint()**: คำนวณจุดต่ำสุดของเหรียญ
2. **Position Correction**: แก้ไขตำแหน่งเมื่อกระทบพื้น
3. **forceFlat()**: บังคับให้เหรียญนอนราบบนพื้น

## 🎮 User Experience

### ความแตกต่างที่เห็นได้:
- **การหมุนต่อเนื่อง**: เหรียญหมุนตลอดจนกว่าจะเด้งต่ำ
- **การหยุดที่เหมาะสม**: หยุดหมุนก่อนที่จะเด้งต่ำเกินไป
- **ป้องกันจมพื้น**: ไม่มีการจมพื้นเพราะหยุดทันเวลา
- **แสดงผลลัพธ์ทันที**: ไม่ต้องรอให้เด้งจนหมด

### ประโยชน์:
1. เหรียญหมุนได้นานขึ้น ดูสมจริงมากขึ้น
2. หยุดหมุนในจังหวะที่เหมาะสม
3. ป้องกันการจมพื้นได้อย่างมีประสิทธิภาพ
4. ผลลัพธ์แสดงทันทีเมื่อเหมาะสม

## 🧪 Testing

ใช้ `demo/bounce-rotation-test.html` เพื่อทดสอบ:
1. เปิดไฟล์ในเบราว์เซอร์
2. กดปุ่มทอยเหรียญ
3. สังเกตการหมุนในช่วงขึ้น-ลง-เด้ง
4. ดูว่าเหรียญหยุดหมุนเมื่อเด้งต่ำ
5. ตรวจสอบ Console เพื่อดู log การทำงาน
6. ยืนยันว่าเหรียญไม่จมพื้น

## 📊 Current Settings (จากการแก้ไขของผู้ใช้)

- **จำนวนเด้งเป้าหมาย**: 2 ครั้ง (ทั้งหัวและก้อย)
- **ความสูงโยน**: 4.0 (ทั้งหัวและก้อย)
- **ความเร็วหมุน**: 1.0 radians/frame
- **ความสูงเหรียญ**: 0.1 units (เป็นเกณฑ์หยุดหมุน)

## 🔮 Technical Details

### การคำนวณความสูงการเด้ง:
```javascript
const nextBounceHeight = Math.abs(this.velocity.y) / Math.abs(this.gravity);
const currentBounceHeight = this.coinPositionY - this.groundY;
const willBounceBelow = nextBounceHeight < this.coinHeight;
```

### การตรวจสอบในแต่ละเฟรม:
- ตรวจสอบ `currentBounceHeight > this.coinHeight` ก่อนหมุน
- หาก `currentBounceHeight ≤ this.coinHeight` จะเรียก `forceFlat()` ทันที

ระบบนี้ทำให้เหรียญมีการหมุนที่สมจริงและหยุดในจังหวะที่เหมาะสม พร้อมป้องกันการจมพื้นได้อย่างมีประสิทธิภาพ
