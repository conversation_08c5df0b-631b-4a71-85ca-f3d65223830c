/**
 * CoinFlipper Module for Vue.js
 * รวม Three.js, AudioManager, และ CoinRenderer เข้าด้วยกันเป็นชุดเดียว
 * สามารถ import ใช้ใน Vue.js ได้โดยตรง
 */

// Import Three.js (จะโหลดจาก CDN หรือ npm package)
let THREE;

// ตรวจสอบว่า Three.js มีอยู่แล้วหรือไม่
if (typeof window !== 'undefined' && window.THREE) {
    THREE = window.THREE;
} else if (typeof require !== 'undefined') {
    try {
        THREE = require('three');
    } catch (e) {
        console.warn('Three.js not found. Please install: npm install three');
    }
}

/**
 * AudioManager Class - จัดการเสียงทั้งหมด
 */
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.initAudioContext();
    }

    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (error) {
            console.warn('Web Audio API not supported');
        }
    }

    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    /**
     * สร้างเสียงการทอยเหรียญที่สมจริง
     * ประกอบด้วย 3 ส่วน: เสียงเสียดสี, เสียงกระเด้ง, และเสียงหมุนช้าลง
     * @param {number} bounceCount - จำนวนการเด้งที่ต้องการ (ถ้าไม่ระบุจะใช้ค่าเริ่มต้น)
     * @param {Object} audioTiming - การตั้งค่าเวลาเสียงตาม pattern (ถ้ามี)
     */
    generateFlipSound(bounceCount = null, audioTiming = null) {
        if (!this.audioContext) return;

        const now = this.audioContext.currentTime;

        // 1. เสียงเสียดสีของโลหะ (white noise + lowpass filter เพื่อความรู้สึกแบบขูด)
        this.playMetalScrape(now, 0.25);

        // 2. เสียงเหรียญกระเด้ง (แยกช่วงเวลาให้เหมือนจริง)
        let bounceTimes;
        if (bounceCount !== null) {
            // สร้าง bounce times ตามจำนวนที่กำหนดและ pattern
            bounceTimes = this.generateBounceTimes(bounceCount, audioTiming);
            console.log('🎵 Audio: Generating sound with custom bounce count:', {
                requestedBounces: bounceCount,
                audioTiming: audioTiming || 'default',
                generatedBounceTimes: bounceTimes,
                totalSoundBounces: bounceTimes.length
            });
        } else {
            // ใช้ค่าเริ่มต้น
            bounceTimes = [0.4, 0.7, 1.0, 1.2, 1.4, 1.55, 1.68];  // 7 ครั้ง
            console.log('🎵 Audio: Using default bounce times:', {
                defaultBounces: bounceTimes.length,
                bounceTimes: bounceTimes
            });
        }

        bounceTimes.forEach((delay, i) => {
            // ความดังลดลงในแต่ละครั้งที่กระเด้ง
            this.playMetalClick(now + delay, 1 - i * 0.1);
        });

        // 3. เสียงหมุนและหยุดช้าๆ (oscillator ความถี่ต่ำแบบโซ่) - เพิ่มระยะเวลา
        const wobbleStartTime = bounceTimes.length > 0 ? Math.max(...bounceTimes) + 0.1 : 1.3;
        this.playWobbleSpin(now + wobbleStartTime, 0.9);
    }

    /**
     * สร้าง bounce times array ตามจำนวนที่กำหนด
     * @param {number} count - จำนวนการเด้ง
     * @param {Object} audioTiming - การตั้งค่าเวลาเสียง (ถ้ามี)
     * @returns {number[]} - array ของเวลาการเด้ง
     */
    generateBounceTimes(count, audioTiming = null) {
        if (count <= 0) return [];

        const times = [];

        // ใช้ค่าจาก audioTiming หรือค่าเริ่มต้น
        const baseDelay = audioTiming?.baseDelay || 0.4;
        const interval = audioTiming?.interval || 0.25;
        const decayFactor = audioTiming?.decayFactor || 0.85;

        let currentDelay = baseDelay;
        let currentInterval = interval;

        for (let i = 0; i < count; i++) {
            times.push(currentDelay);
            currentDelay += currentInterval;
            currentInterval *= decayFactor; // ช่วงเวลาลดลงเรื่อยๆ
        }

        console.log('🎵 Generated bounce times:', {
            count: count,
            audioTiming: audioTiming || 'default',
            generatedTimes: times
        });

        return times;
    }

    playMetalScrape(startTime, duration) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            data[i] = (Math.random() * 2 - 1) * (1 - i / bufferSize); // white noise fade
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(800, startTime);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.3, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + duration);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(startTime);
        noise.stop(startTime + duration);
    }

    playMetalClick(time, volume = 1) {
        if (!this.audioContext) return;

        const bufferSize = this.audioContext.sampleRate * 0.03;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const output = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            output[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / bufferSize, 2);
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'highpass';
        filter.frequency.setValueAtTime(1500, time);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.2 * volume, time);
        gain.gain.exponentialRampToValueAtTime(0.001, time + 0.1);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(time);
        noise.stop(time + 0.1);
    }

    playWobbleSpin(startTime, duration) {
        if (!this.audioContext) return;

        const osc = this.audioContext.createOscillator();
        const gain = this.audioContext.createGain();

        osc.type = 'sine';
        osc.frequency.setValueAtTime(30, startTime);
        osc.frequency.exponentialRampToValueAtTime(8, startTime + duration); // slow wobble

        gain.gain.setValueAtTime(0.08, startTime);
        gain.gain.exponentialRampToValueAtTime(0.001, startTime + duration);

        osc.connect(gain).connect(this.audioContext.destination);
        osc.start(startTime);
        osc.stop(startTime + duration);
    }

    // เสียงชนะ (Epic Music Festival Style)
    generateWinSound() {
        if (!this.audioContext) return;

        console.log('🎵 Playing EPIC win sound - Music Festival Style!');

        // === Main Melody - Triumphant Chord Progression ===
        const mainChord1 = [523.25, 659.25, 783.99]; // C5, E5, G5 (C Major)
        const mainChord2 = [587.33, 739.99, 880.00]; // D5, F#5, A5 (D Major)
        const mainChord3 = [659.25, 830.61, 987.77]; // E5, G#5, B5 (E Major)
        const finalChord = [1046.50, 1318.51, 1567.98, 2093.00]; // C6, E6, G6, C7 (C Major Octave)

        // เล่น chord progression
        [mainChord1, mainChord2, mainChord3, finalChord].forEach((chord, chordIndex) => {
            chord.forEach((freq) => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                // Chain: oscillator -> filter -> gain -> destination
                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.audioContext.destination);

                const startTime = this.audioContext.currentTime + chordIndex * 0.3;

                oscillator.frequency.setValueAtTime(freq, startTime);
                oscillator.type = chordIndex === 3 ? 'sawtooth' : 'sine'; // Final chord more powerful

                // Add some sparkle with filter
                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(freq * 2, startTime);
                filter.frequency.exponentialRampToValueAtTime(freq * 4, startTime + 0.1);

                // Dynamic volume envelope
                const volume = chordIndex === 3 ? 0.15 : 0.12; // Final chord louder
                gainNode.gain.setValueAtTime(0, startTime);
                gainNode.gain.linearRampToValueAtTime(volume, startTime + 0.08);
                gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + (chordIndex === 3 ? 1.2 : 0.6));

                oscillator.start(startTime);
                oscillator.stop(startTime + (chordIndex === 3 ? 1.2 : 0.6));
            });
        });

        // === Confetti Launch Sound Effect ===
        setTimeout(() => {
            this.generateConfettiLaunchSound();
        }, 800); // เล่นหลังจากเสียงหลักเริ่ม

        // === Victory Fanfare ===
        setTimeout(() => {
            this.generateVictoryFanfare();
        }, 1200);
    }

    /**
     * เสียงพิเศษสำหรับการปล่อย confetti
     */
    generateConfettiLaunchSound() {
        if (!this.audioContext) return;

        console.log('🎊 Playing confetti launch sound');

        // เสียง "whoosh" สำหรับการปล่อย confetti
        const whooshDuration = 0.8;
        const startTime = this.audioContext.currentTime;

        // White noise สำหรับ whoosh effect
        const bufferSize = this.audioContext.sampleRate * whooshDuration;
        const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < bufferSize; i++) {
            data[i] = (Math.random() * 2 - 1) * (1 - i / bufferSize); // Fade out noise
        }

        const noise = this.audioContext.createBufferSource();
        noise.buffer = buffer;

        const filter = this.audioContext.createBiquadFilter();
        filter.type = 'bandpass';
        filter.frequency.setValueAtTime(2000, startTime);
        filter.frequency.exponentialRampToValueAtTime(500, startTime + whooshDuration);

        const gain = this.audioContext.createGain();
        gain.gain.setValueAtTime(0.15, startTime);
        gain.gain.exponentialRampToValueAtTime(0.01, startTime + whooshDuration);

        noise.connect(filter).connect(gain).connect(this.audioContext.destination);
        noise.start(startTime);
        noise.stop(startTime + whooshDuration);

        // เสียง "pop" สำหรับการระเบิดของ confetti
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                this.generateConfettiPopSound();
            }, i * 100);
        }
    }

    /**
     * เสียง "pop" สำหรับ confetti แต่ละจุด
     */
    generateConfettiPopSound() {
        if (!this.audioContext) return;

        const startTime = this.audioContext.currentTime;
        const frequency = 800 + Math.random() * 400; // ความถี่สุ่ม

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.5, startTime + 0.1);
        oscillator.type = 'square';

        gainNode.gain.setValueAtTime(0.08, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + 0.15);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.15);
    }

    /**
     * เสียง fanfare ชัยชนะ
     */
    generateVictoryFanfare() {
        if (!this.audioContext) return;

        console.log('🎺 Playing victory fanfare');

        // Trumpet-like fanfare
        const fanfareNotes = [
            { freq: 523.25, time: 0 },    // C5
            { freq: 659.25, time: 0.15 }, // E5
            { freq: 783.99, time: 0.3 },  // G5
            { freq: 1046.50, time: 0.45 }, // C6
            { freq: 1046.50, time: 0.6 },  // C6 (hold)
            { freq: 1318.51, time: 0.9 }   // E6 (finale)
        ];

        fanfareNotes.forEach(note => {
            const startTime = this.audioContext.currentTime + note.time;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.setValueAtTime(note.freq, startTime);
            oscillator.type = 'sawtooth'; // Trumpet-like sound

            // Brass-like filter
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(note.freq * 3, startTime);
            filter.Q.setValueAtTime(5, startTime);

            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.05);
            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.4);

            oscillator.start(startTime);
            oscillator.stop(startTime + 0.4);
        });
    }

    // เสียงแพ้ (descending tone)
    generateLoseSound() {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.5);
        oscillator.type = 'sawtooth';

        gainNode.gain.setValueAtTime(0.2, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.5);
    }
}

/**
 * CoinRenderer Class - จัดการ 3D animation ด้วย Three.js
 */
class CoinRenderer {
    /**
     * Constructor สำหรับ CoinRenderer
     * @param {string|HTMLElement} canvasId - ID ของ canvas element หรือ element โดยตรง
     * @param {Object} options - ตัวเลือกการตั้งค่า
     */
    constructor(canvasId, options = {}) {
        // ตรวจสอบว่า Three.js พร้อมใช้งาน
        if (!THREE) {
            throw new Error('Three.js is required but not found. Please include Three.js before using CoinFlipper.');
        }

        // === ตั้งค่าพื้นฐาน ===
        this.canvas = typeof canvasId === 'string' ? document.getElementById(canvasId) : canvasId;
        this.options = {
            idleSpeed: 0.02,        // ความเร็วการหมุนใน idle mode
            flipDuration: 3000,     // ระยะเวลาการโยน (มิลลิวินาที) - เพิ่มจาก 2000 เป็น 3000
            // การกำหนด settings patterns แบบไม่สุ่ม
            settingsPatterns: {
                heads: {
                    // Pattern สำหรับ heads - โยนสูงและหมุนเร็ว
                    count: 2,
                    maxHeight: 5.0,        // ความสูงสูงสุดที่เหรียญจะขึ้นไป
                    rotationSpeed: 0.4,    // ความเร็วในการหมุนขณะโยนขึ้น (radians per frame)
                },
                tails: {
                    // Pattern สำหรับ tails - โยนต่ำและหมุนช้า
                    count: 2,
                    maxHeight: 5.0,        // ความสูงสูงสุดที่เหรียญจะขึ้นไป
                    rotationSpeed: 0.4,    // ความเร็วในการหมุนขณะโยนขึ้น (radians per frame)
                }
            },
            // เปิด/ปิดการใช้ pattern แทน random selection
            usePatterns: true,
            ...options
        };

        // === สถานะการทำงาน ===
        this.isFlipping = false;            // กำลังโยนเหรียญอยู่หรือไม่
        this.isIdle = false;                // อยู่ใน idle mode หรือไม่
        this.animationId = null;            // ID ของ animation frame

        // === สถานะแอนิเมชัน ===
        this.coinRotationX = 0;             // การหมุนรอบแกน X
        this.coinRotationY = 0;             // การหมุนรอบแกน Y
        this.coinRotationZ = 0;             // การหมุนรอบแกน Z
        this.coinPositionY = 0;             // ตำแหน่ง Y ของเหรียญ
        this.velocity = { x: 0, y: 0, z: 0 };           // ความเร็วในแต่ละแกน
        this.angularVelocity = { x: 0, y: 0, z: 0 };    // ความเร็วเชิงมุมในแต่ละแกน

        // === Camera States และ Animation ===
        this.cameraState = 'idle';          // สถานะกล้อง: 'idle', 'flipping', 'result'
        this.cameraAnimation = {
            isAnimating: false,
            startTime: 0,
            duration: 0,
            startPosition: new THREE.Vector3(),
            targetPosition: new THREE.Vector3(),
            startLookAt: new THREE.Vector3(),
            targetLookAt: new THREE.Vector3(),
            startRotation: new THREE.Euler(),
            targetRotation: new THREE.Euler()
        };

        // === Camera Positions สำหรับแต่ละ State ===
        this.cameraPositions = {
            idle: {
                position: new THREE.Vector3(0, 0.2, 2.5),
                lookAt: new THREE.Vector3(0, -0.5, 0),
                orbitalRadius: 2.5,
                orbitalSpeed: 0.001,
                orbitalAngle: 0
            },
            flipping: {
                basePosition: new THREE.Vector3(0, 0.2, 2.5), // เก็บตำแหน่งเริ่มต้น
                currentPosition: new THREE.Vector3(0, 0.2, 2.5),
                baseLookAt: new THREE.Vector3(0, -0.5, 0), // เก็บ lookAt เริ่มต้น
                currentLookAt: new THREE.Vector3(0, -0.5, 0),
                trackingRatio: 0.15, // กล้องเคลื่อนแค่ 15% ของการเคลื่อนจริงของเหรียญ
                smoothingFactor: 0.08, // ความนุ่มนวลของการเคลื่อน
                easingProgress: 0, // ความคืบหน้าของ easing
                flipStartTime: 0, // เวลาเริ่ม flip

                // === Enhanced Smooth Pitch Control ===
                basePitch: 0, // มุม pitch เริ่มต้น (คำนวณจาก baseLookAt)
                currentPitch: 0, // มุม pitch ปัจจุบัน
                targetPitch: 0, // มุม pitch เป้าหมายที่คำนวณจากตำแหน่งเหรียญ
                maxPitchUp: 15 * Math.PI / 180, // จำกัดมุม pitch ลง (มองลง) ไม่เกิน +15° จาก base
                maxPitchDown: -30 * Math.PI / 180, // อนุญาตให้แหงนขึ้นได้มากขึ้น (มองขึ้น) ถึง -30° จาก base
                pitchLagTime: 150, // ความล่าช้าของมุมกล้อง (มิลลิวินาที) 0.15 วินาที
                pitchSmoothingFactor: 0.04, // ลดความเร็วการปรับมุม pitch เพื่อความนุ่มนวล
                pitchSmoothingFactorAscend: 0.14, // ทำให้เงยขึ้นตอบสนองเร็วในช่วงเหรียญลอยขึ้น

                // การควบคุมการติดตามเฉพาะช่วงเวลา
                minTrackingHeight: -1.0, // เริ่มลดการติดตามเมื่อเหรียญใกล้พื้น
                trackingFadeDistance: 0.8, // ระยะที่เริ่มลดการติดตาม

                // การ dolly in และยกตำแหน่ง (แยกจากการหมุน)
                dollyAmount: 0.3, // ระยะ dolly in (เข้าใกล้เหรียญ)
                liftAmount: 0.2, // ระยะยกกล้องขึ้น

                // === Peak/Descent handling (avoid bouncy follow) ===
                hasReachedPeak: false,
                peakCoinY: 0,
                peakPitch: 0,
                descentStartTime: 0,
                descentStartCameraY: 0,
                descentTargetY: 0,
                descentAnticipationDuration: 600,
                descentHoldPitch: 0,
                descentPitchFollowFactor: 0.15, // ตามลงแบบนุ่มนวล ไม่ 1:1 กล้องตามลง มากขึ้น/น้อยลง
                descentPitchSmoothingFactor: 0.06, // ช้ากว่าช่วงขึ้น เพื่อลดอาการไล่ตามการเด้ง
                descentBiasDown: 4 * Math.PI / 180 // ช่วยก้มลงเล็กน้อยช่วงขาลง
            },
            result: {
                /* ### ค่าที่ต้องปรับ
                ตำแหน่งของ result state อยู่ที่ `cameraPositions.result` โดย result state ใช้ `position` และ `lookAt` ตรงๆ ตอน transition (ค่า `result.pitch` ไม่ได้ถูกใช้งาน)

                ```286:291:src/coin-flipper.js
                result: {
                    position: new THREE.Vector3(0, 1.2, 1.8),
                    lookAt: new THREE.Vector3(0, -1.4, 0),
                    pitch: -35 * Math.PI / 180  // 35 degrees looking down (ไม่ได้ถูกใช้จริง ๆ)
                }
                ```

                มุมก้มที่ได้มาจากความต่างแนวตั้งกับระยะแนวราบระหว่าง `position` กับ `lookAt` โดยประมาณ:
                - ปัจจุบัน: cameraY 1.2, lookAtY -1.4 → ΔY = 2.6, ระยะราบ ≈ 1.8 → atan2(2.6, 1.8) ≈ 55°
                - ต้องการ 65°: ให้ tan(65°) ≈ 2.144 = ΔY / ระยะราบ → ระยะราบที่ต้องการ ≈ 2.6 / 2.144 ≈ 1.21

                ดังนั้นทางที่แนะนำ (ไม่ไปต่ำกว่าพื้น):
                - ปรับ `position.z` จาก 1.8 → ประมาณ 1.21 (ลอง 1.2–1.25 เพื่อจูน)
                - คง `lookAt` เดิมไว้ใกล้พื้น (`y ≈ -1.4` ถึง `-1.45`)

                ตัวอย่างที่แนะนำ:
                - `position: new THREE.Vector3(0, 1.2, 1.22)`
                - `lookAt: new THREE.Vector3(0, -1.45, 0)` (ถ้าจะชิดพื้นจริงขึ้นนิด)

                หมายเหตุ:
                - ไม่แนะนำให้ลด `lookAt.y` ลงไปถึง ~-2.66 เพื่อให้ได้ 65° ขณะคง z=1.8 เพราะจะต่ำกว่าพื้น
                - ค่า `result.pitch` ในบล็อกนี้ไม่ได้ถูกใช้ในโค้ดตอน transition ถ้าจะใช้ pitch จริง ต้องแก้ลอจิกให้คำนวณ lookAt จาก pitch แต่ตอนนี้ไม่จำเป็น

                ถ้าต้องการสูตรทั่วไป:
                - มุมก้ม (degrees) ≈ atan2(cameraY - lookAtY, sqrt(Δx^2 + Δz^2)) × 180/π
                - อยากได้ 65° ให้ตั้ง sqrt(Δx^2 + Δz^2) ≈ (cameraY - lookAtY)/tan(65°)

                - Verification
                - คาดหวัง: หลังแก้ `position.z ≈ 1.21` กล้องจะก้มลงมากขึ้นใกล้ 65°
                - ถ้าเห็นก้มเกิน/น้อยไป ให้จูน `position.z` ทีละ 0.02

                - สรุป
                - แก้ใน `cameraPositions.result`: ลด `position.z` ประมาณ 1.21 และคง `lookAt.y` ใกล้พื้น
                - อย่าไปแก้ `result.pitch` เพราะไม่ได้ถูกใช้ใน transition */
                position: new THREE.Vector3(0, 1.2, 1.2), // เพื่อให้กล้องจะก้มลงมากขึ้นใกล้ 65°
                lookAt: new THREE.Vector3(0, -1.4, 0),
                pitch: -35 * Math.PI / 180  // 35 degrees looking down
            }
        };

        // === ค่าคงที่ Physics ===
        this.gravity = -0.025;              // แรงโน้มถ่วง - ใช้เฉพาะตอนเหรียญตกลงมาและเด้ง
        this.bounceCount = 0;               // จำนวนครั้งที่กระเด้ง
        this.targetBounces = 7;             // จำนวนการเด้งเป้าหมายสำหรับการโยนปัจจุบัน
        this.bounceDamping = 0.7;           // การลดพลังงานเมื่อกระเด้ง - เพิ่มจาก 0.6 เป็น 0.7 เพื่อให้เด้งได้นานขึ้น
        this.rotationDamping = 0.98;        // การลดความเร็วการหมุน
        this.isSettling = false;            // สถานะการปรับตัวหลังจากหยุดกระเด้ง
        this.settleStartTime = 0;           // เวลาเริ่มต้นการปรับตัว

        // === ค่าคงที่สำหรับการโยนแบบใหม่ (Height & Rotation Speed) ===
        this.maxFlipHeight = 3.5;           // ความสูงสูงสุดที่เหรียญจะขึ้นไป (ค่าเริ่มต้น)
        this.flipRotationSpeed = 0.8;       // ความเร็วในการหมุนขณะโยนขึ้น (ค่าเริ่มต้น)
        this.hasReachedPeak = false;        // ตรวจสอบว่าเหรียญถึงจุดสูงสุดแล้วหรือยัง
        this.flipPhase = 'ascending';       // ระยะของการโยน: 'ascending', 'descending', 'bouncing'

        // === ค่าคงที่สำหรับ Collision Detection ===
        this.coinRadius = 0.8;              // รัศมีของเหรียญ
        this.coinHeight = 0.1;              // ความสูงของเหรียญ
        this.groundY = -1.5;                // ตำแหน่ง Y ของพื้น
        this.coinHalfHeight = this.coinHeight / 2;  // ครึ่งความสูงของเหรียญ (0.05)

        // หมายเหตุ: ไม่มี Finish Scene Animation แล้ว - เหรียญจะแสดงผลทันทีเมื่อลงพื้น

        // === เริ่มต้นระบบ ===
        this.initThreeJS();         // สร้าง Three.js scene
        this.createCoin();          // สร้างเหรียญ 3D
        this.createGround();        // สร้างพื้น/ground plane
        this.setupLighting();       // ตั้งค่าแสง
        this.startRenderLoop();     // เริ่ม render loop
    }

    initThreeJS() {
        console.log('🎬 Initializing Three.js...');
        console.log('Canvas element:', this.canvas);

        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xffffff);

        // Get canvas dimensions
        const width = this.canvas.width || this.canvas.clientWidth || 400;
        const height = this.canvas.height || this.canvas.clientHeight || 400;
        console.log('Canvas dimensions:', width, 'x', height);

        this.camera = new THREE.PerspectiveCamera(
            75,
            width / height,
            0.1,
            1000
        );
        // Initialize camera to idle state
        this.setCameraToIdleState();
        console.log('Camera position:', this.camera.position);

        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(width, height);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        console.log('✅ Three.js initialized');
    }

    createCoin() {
        console.log('🪙 Creating coin...');
        // สร้าง geometry สำหรับเหรียญแบบทรงกระบอก
        // CylinderGeometry(radiusTop, radiusBottom, height, radialSegments)
        // - radiusTop: 0.8 = รัศมีด้านบน (เหรียญมีขนาดเท่ากันทั้งสองด้าน)
        // - radiusBottom: 0.8 = รัศมีด้านล่าง (เหรียญมีขนาดเท่ากันทั้งสองด้าน)
        // - height: 0.1 = ความสูง/ความหนาของเหรียญ
        // - radialSegments: 32 = จำนวนเซกเมนต์รอบวง (ยิ่งมากยิ่งกลม แต่ใช้ทรัพยากรมากขึ้น)
        const geometry = new THREE.CylinderGeometry(0.8, 0.8, 0.1, 32);

        // Create textures for heads and tails
        const headsTexture = this.createHeadsTexture();
        const tailsTexture = this.createTailsTexture();
        const edgeTexture = this.createEdgeTexture();

        // Materials array for different faces
        const materials = [
            new THREE.MeshPhongMaterial({ map: edgeTexture }), // Side
            new THREE.MeshPhongMaterial({ map: headsTexture }), // Top (Heads)
            new THREE.MeshPhongMaterial({ map: tailsTexture })  // Bottom (Tails)
        ];

        this.coin = new THREE.Mesh(geometry, materials);
        this.coin.castShadow = true;
        this.coin.receiveShadow = true;

        // Set initial position
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        this.scene.add(this.coin);

        console.log('✅ Coin created and added to scene');
        console.log('Coin position:', this.coin.position);
        console.log('Scene children count:', this.scene.children.length);
    }

    createGround() {
        console.log('🏢 Creating ground plane...');

        // Create ground plane geometry - larger for better reference during camera movement
        const groundGeometry = new THREE.PlaneGeometry(15, 15);

        // Create a subtle grid texture for the ground
        const groundTexture = this.createGroundTexture();

        // Create ground material with enhanced visibility for reference
        const groundMaterial = new THREE.MeshPhongMaterial({
            map: groundTexture,
            color: 0xeeeeee, // Light gray for white theme
            transparent: true,
            opacity: 0.7, // เพิ่มความทึบเพื่อให้เห็นชัดขึ้นเป็น reference
            side: THREE.DoubleSide,
            shininess: 20
        });

        // Create ground mesh
        this.ground = new THREE.Mesh(groundGeometry, groundMaterial);

        // Position the ground at the collision boundary
        this.ground.position.set(0, this.groundY, 0);

        // Rotate the ground to be horizontal (by default PlaneGeometry is vertical)
        this.ground.rotation.x = -Math.PI / 2;

        // Enable shadows
        this.ground.receiveShadow = true;

        // Add to scene
        this.scene.add(this.ground);

        console.log('✅ Ground plane created and added to scene');
        console.log('Ground position:', this.ground.position);
    }

    createGroundTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Fill with light background
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, 256, 256);

        // Draw subtle grid lines
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = 1;
        ctx.globalAlpha = 0.3;

        // Vertical lines
        for (let x = 0; x <= 256; x += 32) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, 256);
            ctx.stroke();
        }

        // Horizontal lines
        for (let y = 0; y <= 256; y += 32) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(256, y);
            ctx.stroke();
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4); // Repeat the pattern to make grid smaller
        return texture;
    }

    createHeadsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Gold gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        // ctx.arc(x, y, radius, startAngle, endAngle)
        // x: 128 - จุดศูนย์กลางแกน X ของวงกลม (ตำแหน่งกึ่งกลางของ canvas 256x256)
        // y: 128 - จุดศูนย์กลางแกน Y ของวงกลม (ตำแหน่งกึ่งกลางของ canvas 256x256)
        // radius: 120 - รัศมีของวงกลม (120 pixels จากจุดศูนย์กลาง)
        // startAngle: 0 - มุมเริ่มต้น (0 radians = ทิศทาง 3 นาฬิกา)
        // endAngle: Math.PI * 2 - มุมสิ้นสุด (2π radians = วงกลมเต็มรอบ 360 องศา)
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the H.jpeg image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load H.jpeg image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#8B4513';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('หัว', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[1]) {
                this.coin.material[1].map.needsUpdate = true;
            }
        };

        //HEADS : Set the image source
        img.src = 'images/h_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        
        // Fix rotation issue with CylinderGeometry UV mapping
        texture.rotation = Math.PI / 2; // Rotate 90 degrees to correct orientation
        texture.center.set(0.5, 0.5); // Set rotation center to middle of texture

        return texture;
    }

    createTailsTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Silver gradient background
        const gradient = ctx.createRadialGradient(128, 128, 0, 128, 128, 128);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.7, '#FFA500');
        gradient.addColorStop(1, '#B8860B');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 256);

        // Border
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.arc(128, 128, 120, 0, Math.PI * 2);
        ctx.stroke();

        // Create a separate canvas for image processing to avoid CORS issues
        const imageCanvas = document.createElement('canvas');
        imageCanvas.width = 256;
        imageCanvas.height = 256;
        const imageCtx = imageCanvas.getContext('2d');

        // Load and draw the coin face image
        const img = new Image();
        img.crossOrigin = 'anonymous'; // Enable CORS

        img.onload = () => {
            // Calculate dimensions to fit the image within the circular border
            const borderRadius = 120;
            const borderDiameter = borderRadius * 2;
            const padding = 20; // Add some padding from the border
            const availableSize = borderDiameter - (padding * 2);

            // Calculate scaling to fit image within available space while maintaining aspect ratio
            const imgAspectRatio = img.width / img.height;
            let drawWidth, drawHeight;

            if (imgAspectRatio > 1) {
                // Image is wider than tall
                drawWidth = availableSize;
                drawHeight = availableSize / imgAspectRatio;
            } else {
                // Image is taller than wide or square
                drawHeight = availableSize;
                drawWidth = availableSize * imgAspectRatio;
            }

            // Center the image
            const drawX = (imageCanvas.width - drawWidth) / 2;
            const drawY = (imageCanvas.height - drawHeight) / 2;

            // Draw image to separate canvas first
            imageCtx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

            // Create circular clipping path on main canvas
            ctx.save();
            ctx.beginPath();
            ctx.arc(128, 128, borderRadius - 10, 0, Math.PI * 2);
            ctx.clip();

            // Draw the processed image from imageCanvas to main canvas
            ctx.drawImage(imageCanvas, 0, 0);

            ctx.restore();

            // Update the texture after image is loaded
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        img.onerror = () => {
            console.error('Failed to load T.png image, falling back to text');
            // Fallback to original text rendering if image fails to load
            ctx.fillStyle = '#2C2C2C';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('ก้อย', 128, 128);

            // Update the texture after fallback rendering
            if (this.coin && this.coin.material && this.coin.material[2]) {
                this.coin.material[2].map.needsUpdate = true;
            }
        };

        // TAILS: Set the image sources
        img.src = 'images/t_image.png';
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;

        // Fix rotation issue with CylinderGeometry UV mapping
        texture.rotation = Math.PI / 2; // Rotate 90 degrees to correct orientation
        texture.center.set(0.5, 0.5); // Set rotation center to middle of texture
        
        return texture;
    }

    createEdgeTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');

        // Gold edge color
        const gradient = ctx.createLinearGradient(0, 0, 0, 32);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(0.5, '#FFA500');
        gradient.addColorStop(1, '#FFD700');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 256, 32);

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        return texture;
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light (main light)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(2, 4, 2);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;

        // Configure shadow camera to cover the ground area
        directionalLight.shadow.camera.left = -6;
        directionalLight.shadow.camera.right = 6;
        directionalLight.shadow.camera.top = 6;
        directionalLight.shadow.camera.bottom = -6;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 20;

        this.scene.add(directionalLight);

        // Point light for extra shine
        const pointLight = new THREE.PointLight(0xffd700, 0.3, 10);
        pointLight.position.set(-2, 2, 2);
        this.scene.add(pointLight);
    }

    // ===== CAMERA CONTROL METHODS =====

    /**
     * ตั้งค่ากล้องสำหรับ Idle State
     * กล้องอยู่ตำแหน่งคงที่ แต่มี slow orbital movement เล็กน้อย
     */
    setCameraToIdleState() {
        this.cameraState = 'idle';
        const idlePos = this.cameraPositions.idle;

        this.camera.position.copy(idlePos.position);
        this.camera.lookAt(idlePos.lookAt);

        // รีเซ็ต orbital angle
        idlePos.orbitalAngle = 0;

        console.log('📷 Camera set to IDLE state');
    }

    /**
     * เปลี่ยนกล้องไปสู่ Flipping State พร้อม smooth pitch control setup
     * เก็บตำแหน่งปัจจุบันเป็นจุดอ้างอิงและเตรียมการติดตามแบบนุ่มนวล
     */
    transitionCameraToFlippingState() {
        if (this.cameraState === 'flipping') return;

        this.cameraState = 'flipping';
        const flippingPos = this.cameraPositions.flipping;

        // 1. เก็บตำแหน่งและการหมุนปัจจุบันของกล้องเป็นจุดอ้างอิง
        // Force camera to center position to prevent diagonal movement appearance
        const centeredPosition = new THREE.Vector3(0, this.camera.position.y, this.camera.position.z);
        flippingPos.basePosition.copy(centeredPosition);
        flippingPos.currentPosition.copy(centeredPosition);
        flippingPos.baseLookAt.copy(new THREE.Vector3(0, -0.5, 0)); // จุด lookAt ปัจจุบัน
        flippingPos.currentLookAt.copy(flippingPos.baseLookAt);

        // คำนวณมุม pitch เริ่มต้นจาก baseLookAt และ basePosition
        const direction = new THREE.Vector3().subVectors(flippingPos.baseLookAt, flippingPos.basePosition);
        direction.normalize();
        flippingPos.basePitch = Math.asin(-direction.y); // มุม pitch เริ่มต้น

        // เริ่มต้นค่า pitch control
        flippingPos.currentPitch = flippingPos.basePitch;
        flippingPos.targetPitch = flippingPos.basePitch;

        // รีเซ็ตค่าการติดตาม
        flippingPos.easingProgress = 0;
        flippingPos.flipStartTime = Date.now();

        // รีเซ็ตสถานะ peak/descend
        flippingPos.hasReachedPeak = false;
        flippingPos.peakCoinY = this.coinPositionY || 0;
        flippingPos.peakPitch = flippingPos.basePitch;
        flippingPos.descentStartTime = 0;
        flippingPos.descentStartCameraY = this.camera.position.y;
        flippingPos.descentTargetY = this.camera.position.y;
        flippingPos.descentHoldPitch = flippingPos.basePitch + flippingPos.maxPitchDown * 0.8; // slightly top-down

        // รีเซ็ตสถานะ bouncing camera
        flippingPos.bouncingStartTime = null;
        flippingPos.bouncingCameraPosition = null;
        flippingPos.bouncingLookAt = null;

        console.log('📷 Camera set to FLIPPING state with smooth pitch control');
        console.log('📷 Base position:', flippingPos.basePosition);
        console.log('📷 Base pitch:', (flippingPos.basePitch * 180 / Math.PI).toFixed(1) + '°');
        console.log('📷 Pitch range: +' + (flippingPos.maxPitchUp * 180 / Math.PI).toFixed(1) + '° to ' + (flippingPos.maxPitchDown * 180 / Math.PI).toFixed(1) + '°');
        console.log('📷 Pitch lag time:', flippingPos.pitchLagTime + 'ms');
        console.log('📷 Smoothing factor:', flippingPos.pitchSmoothingFactor);
    }

    /**
     * เปลี่ยนกล้องไปสู่ Result State (slightly top-down view)
     * 6. จบการ flip - ผ่อนความเคลื่อนไหวและเตรียม transition
     */
    transitionCameraToResultState() {
        if (this.cameraState === 'result') return;

        this.cameraState = 'result';
        const resultPos = this.cameraPositions.result;
        const flippingPos = this.cameraPositions.flipping;

        // ใช้ตำแหน่งปัจจุบันของกล้อง - ถ้าอยู่ในโหมด bouncing ให้ใช้ตำแหน่ง bouncing
        let currentPos, currentLookAt;
        if (this.cameraStoppedTracking && flippingPos.bouncingCameraPosition) {
            // ใช้ตำแหน่งจาก bouncing view
            currentPos = flippingPos.bouncingCameraPosition.clone();
            currentLookAt = flippingPos.bouncingLookAt.clone();
            console.log('📷 Transitioning from bouncing camera view');
        } else {
            // ใช้ตำแหน่งปกติจาก flipping state
            currentPos = flippingPos.currentPosition || this.camera.position;
            currentLookAt = flippingPos.currentLookAt || new THREE.Vector3(0, -0.5, 0);
            console.log('📷 Transitioning from normal flipping view');
        }

        // เริ่ม smooth transition พร้อมผ่อนความเคลื่อนไหวจาก pitch tracking
        this.startCameraTransition(
            currentPos.clone(),
            resultPos.position.clone(),
            currentLookAt.clone(),
            resultPos.lookAt.clone(),
            1200 // เพิ่มเวลาให้การเปลี่ยนแปลงดูนุ่มนวลขึ้น
        );

        console.log('📷 Camera transitioning to RESULT state');
        console.log('📷 From position:', currentPos);
        console.log('📷 To result position:', resultPos.position);
    }

    /**
     * เริ่ม smooth camera transition
     */
    startCameraTransition(startPos, targetPos, startLookAt, targetLookAt, duration) {
        this.cameraAnimation.isAnimating = true;
        this.cameraAnimation.startTime = Date.now();
        this.cameraAnimation.duration = duration;
        this.cameraAnimation.startPosition.copy(startPos);
        this.cameraAnimation.targetPosition.copy(targetPos);
        this.cameraAnimation.startLookAt.copy(startLookAt);
        this.cameraAnimation.targetLookAt.copy(targetLookAt);
    }

    /**
     * อัพเดท camera animation และ behavior ตาม state ปัจจุบัน
     */
    updateCameraAnimation() {
        // Handle smooth transitions
        if (this.cameraAnimation.isAnimating) {
            this.updateCameraTransition();
            return;
        }

        // Handle state-specific camera behavior
        switch (this.cameraState) {
            case 'idle':
                this.updateIdleCameraMovement();
                break;
            case 'flipping':
                this.updateFlippingCameraTracking();
                break;
            case 'result':
                this.updateResultCameraPosition();
                break;
        }
    }

    /**
     * อัพเดท smooth camera transition
     */
    updateCameraTransition() {
        const elapsed = Date.now() - this.cameraAnimation.startTime;
        const progress = Math.min(elapsed / this.cameraAnimation.duration, 1);

        // เลือก easing function ตาม state ปลายทาง
        let easedProgress;
        if (this.cameraState === 'result') {
            // สำหรับ transition ไป result state ใช้ ease-out เพื่อผ่อนความเคลื่อนไหว
            easedProgress = this.easeOutCubic(progress);
        } else {
            // สำหรับ transition อื่นๆ ใช้ ease-in-out ปกติ
            easedProgress = this.easeInOutCubic(progress);
        }

        // Interpolate position
        this.camera.position.lerpVectors(
            this.cameraAnimation.startPosition,
            this.cameraAnimation.targetPosition,
            easedProgress
        );

        // Interpolate lookAt
        const currentLookAt = new THREE.Vector3().lerpVectors(
            this.cameraAnimation.startLookAt,
            this.cameraAnimation.targetLookAt,
            easedProgress
        );

        this.camera.lookAt(currentLookAt);

        // จบ transition
        if (progress >= 1) {
            this.cameraAnimation.isAnimating = false;
            console.log(`📷 Camera transition to ${this.cameraState.toUpperCase()} completed`);
        }
    }

    /**
     * Ease-out cubic function สำหรับการผ่อนความเคลื่อนไหว
     */
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    /**
     * Ease-out quartic function สำหรับการหมุนที่เป็นธรรมชาติ
     * เริ่มเร็ว แล้วค่อยๆ ช้าลง
     */
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }

    /**
     * อัพเดท camera movement สำหรับ Idle State
     * Slow orbital movement รอบเหรียญ
     */
    updateIdleCameraMovement() {
        const idlePos = this.cameraPositions.idle;

        // Slow orbital movement
        idlePos.orbitalAngle += idlePos.orbitalSpeed;

        // คำนวณตำแหน่งใหม่รอบวงกลม
        const x = Math.sin(idlePos.orbitalAngle) * idlePos.orbitalRadius * 0.1; // เคลื่อนไหวเล็กน้อย
        const z = idlePos.orbitalRadius + Math.cos(idlePos.orbitalAngle) * 0.2;

        this.camera.position.set(x, idlePos.position.y, z);
        this.camera.lookAt(idlePos.lookAt);
    }

    /**
     * อัพเดท camera tracking สำหรับ Flipping State - Direct Look At Coin
     * กล้องติดตามเหรียญแบบนุ่มนวลและมองตรงไปที่เหรียญเพื่อให้เห็นการเคลื่อนที่แนวตั้งที่แท้จริง
     */
    updateFlippingCameraTracking() {
        const flippingPos = this.cameraPositions.flipping;
        const elapsed = Date.now() - flippingPos.flipStartTime;

        // คำนวณตำแหน่งของเหรียญใน world space ทุกเฟรม
        const coinWorldPosition = new THREE.Vector3(0, this.coinPositionY, 0);

        // 🎬 ตรวจสอบว่ากล้องควรหยุดติดตามหรือไม่ (เมื่อเหรียญเริ่มกระเด้ง)
        if (this.cameraStoppedTracking) {
            // กล้องหยุดติดตาม - ปรับตำแหน่งให้ zoom out เพื่อมองเห็นการกระเด้งได้ดีขึ้น
            this.updateBouncingCameraView(flippingPos, elapsed);

            // Debug info
            if (elapsed % 1000 < 16) {
                console.log('📷 Camera in bouncing view mode:', {
                    coinY: this.coinPositionY.toFixed(3),
                    bounceCount: this.bounceCount,
                    cameraPos: `(${this.camera.position.x.toFixed(1)}, ${this.camera.position.y.toFixed(1)}, ${this.camera.position.z.toFixed(1)})`,
                    lookAt: `(${flippingPos.currentLookAt.x.toFixed(1)}, ${flippingPos.currentLookAt.y.toFixed(1)}, ${flippingPos.currentLookAt.z.toFixed(1)})`
                });
            }
            return;
        }

        // ใช้ easing เพื่อความสมูท (ease-in → fast → ease-out)
        const flipDuration = 3000; // ประมาณระยะเวลา flip - เพิ่มจาก 2000 เป็น 3000
        const normalizedTime = Math.min(elapsed / flipDuration, 1);
        const easingFactor = this.createFlipEasing(normalizedTime);

        // === 1. การเคลื่อนตำแหน่งกล้อง (Position Tracking) - แยกจากการหมุน ===
        const coinMovementY = this.coinPositionY; // การเคลื่อนจริงของเหรียญ

        // ตรวจจับจุดสูงสุดของเหรียญ: เมื่อความเร็วแกน Y เปลี่ยนจากบวกเป็นลบ
        const isAscending = this.velocity?.y > 0;
        const justStartedDescent = !isAscending && !flippingPos.hasReachedPeak;
        if (justStartedDescent) {
            flippingPos.hasReachedPeak = true;
            flippingPos.peakCoinY = this.coinPositionY;
            flippingPos.peakPitch = flippingPos.currentPitch;
            flippingPos.descentStartTime = Date.now();
            flippingPos.descentStartCameraY = flippingPos.currentPosition.y;
            // target Y ช่วงลง: ค่อยๆ ลดลงเล็กน้อยไปสู่ค่าสบายตา แทนการตามเหรียญจนถึงพื้น
            const slightDrop = 0.1; // ลดลงเล็กน้อยเตรียมจับผล
            flippingPos.descentTargetY = flippingPos.basePosition.y + slightDrop;
        }

        // คำนวณตำแหน่งเป้าหมายของกล้อง (รวม dolly in และ lift)
        const targetPosition = flippingPos.basePosition.clone();

        // ช่วงขึ้น: ตาม Y แบบจำกัด, ช่วงลง: อยู่นิ่ง/ลดลงเล็กน้อย (anticipate) ไม่ตามเหรียญ
        let targetY;
        if (!flippingPos.hasReachedPeak) {
            // Ascend: follow limited Y + lift
            const cameraMovementYAsc = coinMovementY * flippingPos.trackingRatio;
            targetY = flippingPos.basePosition.y + cameraMovementYAsc + (flippingPos.liftAmount * easingFactor);
        } else {
            // Descent: ease from descentStartCameraY to descentTargetY over a short duration
            const t = Math.min((Date.now() - flippingPos.descentStartTime) / Math.max(1, flippingPos.descentAnticipationDuration), 1);
            const easedT = this.easeOutCubic(t);
            targetY = this.lerp(flippingPos.descentStartCameraY, flippingPos.descentTargetY, easedT);
        }
        targetPosition.y = targetY;

        // Dolly in เข้าใกล้เหรียญเล็กน้อย - DISABLED to prevent diagonal movement appearance
        // const dollyProgress = easingFactor * flippingPos.dollyAmount;
        // targetPosition.z -= dollyProgress; // เข้าใกล้เหรียญ

        // อัพเดทตำแหน่งกล้องแบบ smooth (แยกจากการหมุน)
        flippingPos.currentPosition.lerp(targetPosition, flippingPos.smoothingFactor * easingFactor);

        // === 2. การปรับมุมกล้องให้มองตรงๆ ไปที่เหรียญ ===
        // ตั้งตำแหน่งกล้อง
        this.camera.position.copy(flippingPos.currentPosition);

        // มองตรงไปที่เหรียญโดยตรง - ไม่ใช้การคำนวณ pitch ที่ซับซ้อน
        this.camera.lookAt(coinWorldPosition);

        // เก็บค่า lookAt ปัจจุบันไว้
        flippingPos.currentLookAt.copy(coinWorldPosition);

        // Debug info (ลดความถี่)
        if (elapsed % 500 < 16) {
            console.log('📷 Direct camera tracking for vertical movement:', {
                coinY: coinMovementY.toFixed(3),
                cameraY: flippingPos.currentPosition.y.toFixed(3),
                lookingAt: `(${coinWorldPosition.x.toFixed(1)}, ${coinWorldPosition.y.toFixed(1)}, ${coinWorldPosition.z.toFixed(1)})`,
                cameraPos: `(${flippingPos.currentPosition.x.toFixed(1)}, ${flippingPos.currentPosition.y.toFixed(1)}, ${flippingPos.currentPosition.z.toFixed(1)})`
            });
        }
    }

    /**
     * สร้าง easing function สำหรับ flip animation
     * เริ่มช้า → เร็ว → ช้า เพื่อความสมูท
     */
    createFlipEasing(t) {
        // ใช้ cubic easing แต่ปรับให้เหมาะกับ flip motion
        if (t < 0.3) {
            // ช่วงแรก: ค่อยๆ เร่ง (ease-in)
            return 2 * t * t;
        } else if (t < 0.7) {
            // ช่วงกลาง: เคลื่อนเร็วสุด
            return 1;
        } else {
            // ช่วงท้าย: ค่อยๆ ช้าลง (ease-out)
            const adjustedT = (t - 0.7) / 0.3;
            return 1 - 0.5 * adjustedT * adjustedT;
        }
    }

    /**
     * อัพเดท camera position สำหรับ Result State
     * รักษาตำแหน่ง top-down view ให้คงที่
     */
    updateResultCameraPosition() {
        // ไม่ต้องทำอะไรเพิ่มเติม - กล้องอยู่ในตำแหน่งที่ต้องการแล้ว
        // อาจเพิ่ม subtle breathing effect ได้ถ้าต้องการ
    }

    /**
     * อัพเดท camera view สำหรับช่วงที่เหรียญกำลังกระเด้ง
     * ปรับกล้องให้ zoom out และมองเห็นการกระเด้งได้ดีขึ้น
     */
    updateBouncingCameraView(flippingPos, elapsed) {
        // ตั้งค่าการ zoom out สำหรับมองเห็นการกระเด้ง
        const bouncingCameraSettings = {
            zoomOutDistance: 1.5,    // ระยะ zoom out เพิ่มเติม
            heightAdjustment: 0.8,   // ยกกล้องขึ้นเพิ่มเติม
            transitionDuration: 500, // ระยะเวลาในการ transition ไปสู่ bouncing view
            lookAtGroundOffset: -0.2 // มองลงไปที่พื้นเล็กน้อยเพื่อเห็นการกระเด้ง
        };

        // คำนวณเวลาที่ผ่านไปตั้งแต่เริ่ม bouncing
        const bouncingStartTime = flippingPos.bouncingStartTime || (flippingPos.bouncingStartTime = Date.now());
        const bouncingElapsed = Date.now() - bouncingStartTime;
        const transitionProgress = Math.min(bouncingElapsed / bouncingCameraSettings.transitionDuration, 1);
        const easedProgress = this.easeOutCubic(transitionProgress);

        // คำนวณตำแหน่งกล้องสำหรับ bouncing view
        const basePosition = flippingPos.currentPosition.clone();
        const targetBouncingPosition = new THREE.Vector3(
            basePosition.x,
            basePosition.y + bouncingCameraSettings.heightAdjustment,
            basePosition.z + bouncingCameraSettings.zoomOutDistance
        );

        // Lerp ไปสู่ตำแหน่ง bouncing view
        const currentBouncingPosition = basePosition.clone().lerp(targetBouncingPosition, easedProgress);
        this.camera.position.copy(currentBouncingPosition);

        // ปรับ lookAt ให้มองลงไปที่พื้นเล็กน้อยเพื่อเห็นการกระเด้งดีขึ้น
        const baseLookAt = flippingPos.currentLookAt.clone();
        const targetBouncingLookAt = new THREE.Vector3(
            baseLookAt.x,
            baseLookAt.y + bouncingCameraSettings.lookAtGroundOffset,
            baseLookAt.z
        );

        const currentBouncingLookAt = baseLookAt.clone().lerp(targetBouncingLookAt, easedProgress);
        this.camera.lookAt(currentBouncingLookAt);

        // เก็บค่าปัจจุบันไว้ใน flippingPos สำหรับการ transition ต่อไป
        flippingPos.bouncingCameraPosition = currentBouncingPosition;
        flippingPos.bouncingLookAt = currentBouncingLookAt;
    }

    startRenderLoop() {
        console.log('✅ Starting render loop');
        const animate = () => {
            this.animationId = requestAnimationFrame(animate);
            this.updateAnimation();
            this.updateCameraAnimation(); // อัพเดท camera animation
            this.renderer.render(this.scene, this.camera);
        };
        animate();
    }

    /**
     * อัพเดทแอนิเมชันหลัก - เรียกในทุกเฟรมเพื่อจัดการแอนิเมชันต่างๆ
     * ตัดสินใจว่าจะเรียกแอนิเมชันแบบไหนตามสถานะปัจจุบัน
     */
    updateAnimation() {
        if (this.isIdle && !this.isFlipping) {
            // === Idle Animation ===
            // หมุนเหรียญเบาๆ รอบแกน X เมื่ออยู่ในสถานะพัก (single-axis rotation)
            this.coinRotationX += this.options.idleSpeed;
            this.coin.rotation.x = this.coinRotationX;
            // ตรวจสอบให้แน่ใจว่าไม่มีการหมุนในแกนอื่น
            this.coin.rotation.y = 0;
            this.coin.rotation.z = 0;
        } else if (this.isFlipping) {
            // === Flip Animation ===
            // แอนิเมชันการโยนเหรียญ - จำลอง physics และการกระเด้ง
            this.updateFlipAnimation();
        }
        // หมายเหตุ: ไม่มี Finish Scene Animation แล้ว - เหรียญจะแสดงผลทันทีเมื่อลงพื้น
    }

    /**
     * เลือกจำนวนการ bounce จาก set ที่กำหนดตามผลลัพธ์
     * @param {string} result - ผลลัพธ์ที่ต้องการ ('heads' หรือ 'tails')
     * @returns {number} - จำนวนการ bounce ที่เลือก
     */
    selectBounceCount(result) {
        // ตรวจสอบว่าใช้ pattern หรือ random selection
        if (this.options.usePatterns && this.options.settingsPatterns[result]) {
            return this.selectBouncePattern(result);
        }

        // ใช้ค่าเริ่มต้นถ้าไม่มี pattern
        const defaultBounceSet = [5, 6, 7, 8];
        const randomIndex = Math.floor(Math.random() * defaultBounceSet.length);
        const selectedBounceCount = defaultBounceSet[randomIndex];

        console.log(`🎯 Bounce Selection for ${result}:`, {
            setType: 'default',
            availableSet: defaultBounceSet,
            selectedIndex: randomIndex,
            selectedBounceCount: selectedBounceCount
        });

        return selectedBounceCount;
    }

    /**
     * เลือก bounce pattern ที่กำหนดไว้แทนการสุ่ม
     * @param {string} result - ผลลัพธ์ที่ต้องการ ('heads' หรือ 'tails')
     * @returns {number} - จำนวนการ bounce ตาม pattern
     */
    selectBouncePattern(result) {
        const pattern = this.options.settingsPatterns[result];

        if (!pattern) {
            console.warn(`⚠️ No settings pattern found for ${result}, using default`);
            return 5;
        }

        // ตั้งค่า height และ rotation speed parameters ตาม pattern
        this.maxFlipHeight = pattern.maxHeight;
        this.flipRotationSpeed = pattern.rotationSpeed;

        console.log(`🎯 Settings Pattern Selected for ${result}:`, {
            count: pattern.count,
            maxHeight: pattern.maxHeight,
            rotationSpeed: pattern.rotationSpeed,
            audioTiming: pattern.audioTiming
        });

        return pattern.count;
    }

    /**
     * เลือกความสูงและความเร็วหมุนตามผลลัพธ์
     * @param {string} result - ผลลัพธ์ที่ต้องการ ('heads' หรือ 'tails')
     * @returns {Object} - {height, rotationSpeed}
     */
    selectHeightAndRotationSpeed(result) {
        // ตรวจสอบว่าใช้ pattern หรือ random selection
        if (this.options.usePatterns && this.options.settingsPatterns[result]) {
            const pattern = this.options.settingsPatterns[result];
            return {
                height: pattern.maxHeight,
                rotationSpeed: pattern.rotationSpeed
            };
        }

        // ใช้ค่าเริ่มต้นถ้าไม่มี pattern
        const defaultHeight = result === 'heads' ? 3.8 : 3.2;
        const defaultRotationSpeed = result === 'heads' ? 0.85 : 0.75;

        console.log(`🎯 Height & Rotation Selection for ${result}:`, {
            setType: 'default',
            selectedHeight: defaultHeight,
            selectedRotationSpeed: defaultRotationSpeed
        });

        return {
            height: defaultHeight,
            rotationSpeed: defaultRotationSpeed
        };
    }

    /**
     * เปิดใช้งาน settings patterns แทนการสุ่ม
     * @param {boolean} enable - เปิด (true) หรือปิด (false) การใช้ patterns
     */
    enableSettingsPatterns(enable = true) {
        this.options.usePatterns = enable;
        console.log(`🎯 Settings patterns ${enable ? 'enabled' : 'disabled'}`);
    }

    /**
     * ตั้งค่า settings pattern ใหม่สำหรับผลลัพธ์ที่กำหนด
     * @param {string} result - ผลลัพธ์ ('heads' หรือ 'tails')
     * @param {Object} pattern - การตั้งค่า pattern ใหม่
     */
    setSettingsPattern(result, pattern) {
        if (!this.options.settingsPatterns) {
            this.options.settingsPatterns = {};
        }

        this.options.settingsPatterns[result] = {
            count: pattern.count || 5,
            damping: pattern.damping || 0.7,
            gravity: pattern.gravity || -0.02,
            audioTiming: {
                baseDelay: pattern.audioTiming?.baseDelay || 0.4,
                interval: pattern.audioTiming?.interval || 0.25,
                decayFactor: pattern.audioTiming?.decayFactor || 0.85
            }
        };

        console.log(`🎯 Settings pattern set for ${result}:`, this.options.settingsPatterns[result]);
    }

    /**
     * รีเซ็ต physics parameters กลับสู่ค่าเริ่มต้น
     */
    resetPhysicsParameters() {
        this.gravity = -0.02;
        this.bounceDamping = 0.7;
        this.rotationDamping = 0.98;
        console.log('🔄 Physics parameters reset to default');
    }

    /**
     * คำนวณจุดที่ต่ำที่สุดของเหรียญโดยคำนึงถึงการหมุน
     * เมื่อเหรียญหมุน จุดที่ต่ำที่สุดอาจเป็นขอบของเหรียญ
     */
    calculateLowestPoint() {
        // เมื่อเหรียญหมุนรอบแกน X, จุดที่ต่ำที่สุดจะเป็น:
        // - เมื่อ rotation.x = 0 (นอนราบ): จุดต่ำสุด = center.y - coinHalfHeight
        // - เมื่อ rotation.x = π/2 (ตั้งขึ้น): จุดต่ำสุด = center.y - coinRadius
        // - เมื่อ rotation.x = π (คว่ำ): จุดต่ำสุด = center.y - coinHalfHeight

        const rotationX = this.coinRotationX;

        // คำนวณระยะจากจุดศูนย์กลางไปยังจุดที่ต่ำที่สุด
        // ใช้ sin และ cos เพื่อหาจุดที่ต่ำที่สุดเมื่อเหรียญหมุน
        const verticalOffset = Math.max(
            this.coinHalfHeight * Math.abs(Math.cos(rotationX)), // ความสูงจากการนอนราบ/คว่ำ
            this.coinRadius * Math.abs(Math.sin(rotationX))      // ความสูงจากการตั้งขึ้น
        );

        return this.coinPositionY - verticalOffset;
    }

    /**
     * อัพเดทแอนิเมชันการโยนเหรียญ - ใช้ระบบ physics แบบใหม่
     * ช่วงขึ้น: ใช้ความสูงและความเร็วหมุนที่กำหนด
     * ช่วงลง: ใช้แรงโน้มถ่วงเมื่อเริ่มตกและเด้ง
     */
    updateFlipAnimation() {
        // === การจำลอง Physics แบบใหม่ ===
        if (this.flipPhase === 'ascending') {
            // ช่วงขึ้น: ใช้ความสูงและความเร็วหมุนที่กำหนด
            this.updateAscendingPhase();
        } else if (this.flipPhase === 'descending' || this.flipPhase === 'bouncing') {
            // ช่วงลงและเด้ง: ใช้แรงโน้มถ่วง
            this.updateDescendingPhase();
        }

        // อัพเดทการหมุนของเหรียญ
        this.updateCoinRotation();

        // อัพเดทตำแหน่งเหรียญใน 3D space
        this.coin.position.y = this.coinPositionY;
        this.coin.rotation.x = this.coinRotationX;
        this.coin.rotation.y = this.coinRotationY;
        this.coin.rotation.z = this.coinRotationZ;
    }

    /**
     * อัพเดทช่วงขึ้น - ใช้ความสูงและความเร็วหมุนที่กำหนด
     */
    updateAscendingPhase() {
        if (!this.flipStartTime) return;

        const elapsed = Date.now() - this.flipStartTime;
        const ascendDuration = this.options.flipDuration * 0.4; // 40% ของเวลาทั้งหมดสำหรับขึ้น
        const progress = Math.min(elapsed / ascendDuration, 1);

        // ใช้ easing function สำหรับการขึ้นแบบธรรมชาติ (เร็วแล้วช้าลง)
        const easedProgress = this.easeOutQuart(progress);

        // คำนวณความสูงปัจจุบัน
        this.coinPositionY = this.maxFlipHeight * easedProgress;

        // ตรวจสอบว่าถึงจุดสูงสุดแล้วหรือยัง
        if (progress >= 1) {
            this.flipPhase = 'descending';
            this.hasReachedPeak = true;
            // เริ่มต้นความเร็วสำหรับการตกลง
            this.velocity.y = 0;
            console.log('🎯 Coin reached peak height:', this.coinPositionY.toFixed(3));
        }
    }

    /**
     * อัพเดทช่วงลงและเด้ง - ใช้แรงโน้มถ่วง
     */
    updateDescendingPhase() {
        // ใช้แรงโน้มถ่วงเฉพาะในช่วงนี้
        this.velocity.y += this.gravity;
        this.coinPositionY += this.velocity.y;

        // เปลี่ยนเป็น bouncing phase เมื่อเริ่มกระทบพื้น
        if (this.flipPhase === 'descending' && this.coinPositionY <= this.groundY + this.coinHalfHeight) {
            this.flipPhase = 'bouncing';
            console.log('🎯 Coin entered bouncing phase');
        }
    }

    /**
     * อัพเดทการหมุนของเหรียญ
     */
    updateCoinRotation() {
        if (this.flipPhase === 'ascending') {
            // ช่วงขึ้น: ใช้ความเร็วหมุนที่กำหนด
            this.coinRotationX += this.flipRotationSpeed;
        } else if (this.flipPhase === 'descending') {
            // ช่วงลง: ใช้ความเร็วหมุนที่กำหนดต่อไป
            this.coinRotationX += this.flipRotationSpeed;
        } else if (this.flipPhase === 'bouncing') {
            // ช่วงเด้ง: ให้เด้งได้อย่างน้อย 1 ครั้งก่อนตรวจสอบความสูง
            const currentBounceHeight = this.coinPositionY - this.groundY;

            // หยุดหมุนเมื่อเด้งแล้วอย่างน้อย 1 ครั้ง และความสูงการเด้งต่ำกว่าความสูงของเหรียญ
            if (this.bounceCount >= 1 && currentBounceHeight <= this.coinHeight) {
                // เด้งแล้วและต่ำกว่าความสูงเหรียญแล้ว - หยุดหมุนและแสดงผลลัพธ์
                console.log('🎯 Bounced at least once and height below coin height - stopping rotation and showing result');
                this.forceFlat(this.winLoseState); // ส่ง winLoseState เพื่อแสดง WIN/LOSE scene
                return; // ออกจากฟังก์ชันเพื่อไม่ให้ไปทำ handleBouncing()
            } else {
                // ยังไม่เด้งครบหรือยังสูงกว่าความสูงเหรียญ - หมุนต่อไป
                this.coinRotationX += this.flipRotationSpeed * 0.8; // หมุนช้าลงเล็กน้อยในช่วงเด้ง
            }
        }

        // รีเซ็ตการหมุนในแกนอื่นให้เป็น 0
        this.coinRotationY = 0;
        this.coinRotationZ = 0;

        // === การกระเด้ง (Bouncing) - เฉพาะในช่วง bouncing phase ===
        if (this.flipPhase === 'bouncing') {
            this.handleBouncing();
        }
    }

    /**
     * จัดการการกระเด้งของเหรียญ
     */
    handleBouncing() {
        // คำนวณจุดที่ต่ำที่สุดของเหรียญโดยคำนึงถึงการหมุน
        const lowestPointY = this.calculateLowestPoint();

        // ตรวจสอบว่าจุดที่ต่ำที่สุดของเหรียญกระทบพื้น และกำลังตกลงมา (velocity.y < 0)
        if (lowestPointY <= this.groundY && this.velocity.y < 0) {
            // คำนวณตำแหน่งใหม่ของจุดศูนย์กลางเหรียญเพื่อให้จุดที่ต่ำที่สุดอยู่บนพื้นพอดี
            const correctionY = this.groundY - lowestPointY;
            this.coinPositionY += correctionY;

            // Debug logging สำหรับการแก้ไขตำแหน่ง
            if (this.bounceCount < 3) {
                console.log('🏀 Bounce collision detected:', {
                    bounceCount: this.bounceCount + 1,
                    coinCenterY: this.coinPositionY.toFixed(3),
                    lowestPointY: lowestPointY.toFixed(3),
                    groundY: this.groundY.toFixed(3),
                    correctionY: correctionY.toFixed(4),
                    rotationX: (this.coinRotationX * 180 / Math.PI).toFixed(1) + '°',
                    velocityY: this.velocity.y.toFixed(4)
                });
            }

            // กลับทิศความเร็วและลดลงตาม bounceDamping
            this.velocity.y = -this.velocity.y * this.bounceDamping;

            // นับจำนวนครั้งที่กระเด้ง
            this.bounceCount++;

            // คำนวณความสูงการเด้งหลังจากการกระเด้งนี้
            const nextBounceHeight = Math.abs(this.velocity.y) / Math.abs(this.gravity);
            const currentBounceHeight = this.coinPositionY - this.groundY;

            // Log การเด้งแต่ละครั้ง
            console.log(`🏀 Bounce #${this.bounceCount}/${this.targetBounces}:`, {
                bounceProgress: `${this.bounceCount}/${this.targetBounces}`,
                velocityAfterBounce: this.velocity.y.toFixed(4),
                coinPositionY: this.coinPositionY.toFixed(3),
                currentBounceHeight: currentBounceHeight.toFixed(3),
                nextBounceHeight: nextBounceHeight.toFixed(3),
                coinHeight: this.coinHeight.toFixed(3),
                rotationX: (this.coinRotationX * 180 / Math.PI).toFixed(1) + '°'
            });

            // 🎬 หยุดกล้องติดตามเมื่อเหรียญเริ่มกระเด้งครั้งแรก
            if (this.bounceCount === 1) {
                this.cameraStoppedTracking = true;
                console.log('📷 Camera stopped tracking - coin started bouncing');
            }

            // หยุดการกระเด้งเมื่อ:
            // 1. กระเด้งครบจำนวนเป้าหมายและความเร็วต่ำพอ หรือ
            // 2. เด้งแล้วอย่างน้อย 1 ครั้งและความสูงการเด้งต่อไปจะต่ำกว่าความสูงของเหรียญ
            const willBounceBelow = nextBounceHeight < this.coinHeight;
            const hasBouncedEnough = this.bounceCount >= 1;
            if ((this.bounceCount >= this.targetBounces && Math.abs(this.velocity.y) < 0.12) || (hasBouncedEnough && willBounceBelow)) {
                const stopReason = (hasBouncedEnough && willBounceBelow) ? 'Bounced enough and height below coin height' : 'Target bounces reached';
                console.log('🛑 Bounce Test Completed - Stopping bounces:', {
                    stopReason: stopReason,
                    actualBounces: this.bounceCount,
                    targetBounces: this.targetBounces,
                    bounceMatched: this.bounceCount >= this.targetBounces ? '✅ Target Reached' : '❌ Target Not Reached',
                    hasBouncedEnough: hasBouncedEnough ? '✅ Yes' : '❌ No',
                    nextBounceHeight: nextBounceHeight.toFixed(3),
                    coinHeight: this.coinHeight.toFixed(3),
                    willBounceBelow: willBounceBelow ? '✅ Yes' : '❌ No',
                    finalVelocityY: this.velocity.y.toFixed(4),
                    currentRotationX: (this.coinRotationX * 180 / Math.PI).toFixed(1) + '°',
                    currentPositionY: this.coinPositionY.toFixed(3)
                });

                // หยุดการเคลื่อนไหวทันที
                this.velocity.y = 0;

                // บังคับให้การหมุนไปสู่ตำแหน่งสุดท้ายที่ถูกต้อง
                if (this.targetRotationX !== undefined) {
                    this.coinRotationX = this.targetRotationX;
                    console.log('🎯 Forced rotation to target:', {
                        targetRotationX: (this.targetRotationX * 180 / Math.PI).toFixed(1) + '°',
                        finalResult: this.intendedResult
                    });
                }

                // บังคับให้เหรียญนอนราบกับพื้นทันทีตามผลลัพธ์ที่ต้องการ
                this.forceFlat(this.winLoseState);
            }
        }
    }

    /**
     * บังคับให้เหรียญนอนราบกับพื้นทันที
     * @param {string|null} winLoseState - 'win', 'lose', หรือ null สำหรับแสดงผลปกติ
     */
    forceFlat(winLoseState = null) {
        console.log('🎯 Forcing coin to lie flat immediately');

        // ได้ผลลัพธ์ที่ต้องการ
        const finalResult = this.intendedResult || this.getCurrentResult();

        console.log('🎯 Final result determined:', finalResult);

        // บังคับตั้งค่าการหมุนให้เหรียญนอนราบทันที
        if (finalResult === 'heads') {
            // หัว: หน้าบน (top face) หันขึ้น
            this.coin.rotation.x = 0;           // นอนราบ
            this.coin.rotation.y = 0;           // ไม่หมุน
            this.coin.rotation.z = 0;           // ไม่หมุน
            this.coinRotationX = 0;
            this.coinRotationY = 0;
            this.coinRotationZ = 0;
        } else {
            // ก้อย: หน้าล่าง (bottom face) หันขึ้น
            this.coin.rotation.x = Math.PI;     // หมุน 180° เพื่อให้หน้าล่างหันขึ้น
            this.coin.rotation.y = 0;           // ไม่หมุน
            this.coin.rotation.z = 0;           // ไม่หมุน
            this.coinRotationX = Math.PI;
            this.coinRotationY = 0;
            this.coinRotationZ = 0;
        }

        // บังคับตั้งตำแหน่งให้เหรียญอยู่บนพื้นพอดี
        // เมื่อเหรียญนอนราบ (rotation.x = 0 หรือ π) จุดต่ำสุดจะอยู่ที่ center.y - coinHalfHeight
        const flatPositionY = this.groundY + this.coinHalfHeight;
        this.coin.position.y = flatPositionY;
        this.coinPositionY = flatPositionY;

        // ตรวจสอบให้แน่ใจว่าเหรียญไม่จมพื้นหลังจากบังคับให้นอนราบ
        const finalLowestPoint = this.calculateLowestPoint();
        if (finalLowestPoint < this.groundY) {
            const additionalCorrection = this.groundY - finalLowestPoint;
            this.coin.position.y += additionalCorrection;
            this.coinPositionY += additionalCorrection;
            console.log('🔧 Additional position correction applied:', additionalCorrection.toFixed(4));
        }

        // รีเซ็ตสถานะ
        this.isFlipping = false;
        this.isSettling = false;

        // เปลี่ยนกล้องไปสู่ result state
        this.transitionCameraToResultState();

        console.log('🎯 Coin forced to flat position:', {
            result: finalResult,
            winLoseState: winLoseState,
            rotation: {
                x: (this.coin.rotation.x * 180 / Math.PI).toFixed(1) + '°',
                y: (this.coin.rotation.y * 180 / Math.PI).toFixed(1) + '°',
                z: (this.coin.rotation.z * 180 / Math.PI).toFixed(1) + '°'
            },
            position: {
                y: this.coin.position.y.toFixed(3),
                expected: flatPositionY.toFixed(3)
            },
            isFlat: Math.abs(this.coin.rotation.x) < 0.01 || Math.abs(this.coin.rotation.x - Math.PI) < 0.01
        });

        // เพิ่มเอฟเฟกต์เพื่อเน้นผลลัพธ์ พร้อม win/lose scene
        this.enhanceFinalResult(winLoseState);

        // ส่งผลลัพธ์กลับ
        if (this.flipResolve) {
            this.flipResolve(finalResult);
            this.flipResolve = null;
        }

        console.log('✅ Bounce Test Summary - Flip Completed:', {
            finalResult: finalResult,
            targetBounces: this.targetBounces,
            actualBounces: this.bounceCount,
            bounceTestPassed: this.bounceCount >= this.targetBounces ? '✅ PASS' : '❌ FAIL',
            usedPattern: this.options.usePatterns ? 'settings pattern' : 'default'
        });
    }

    /**
     * เริ่มการปรับตัวแบบค่อยๆ หยุด (settling animation)
     * หมายเหตุ: ฟังก์ชันนี้ไม่ถูกใช้แล้วเพราะใช้ forceFlat() แทน
     */
    startSettlingAnimation() {
        // เก็บการหมุนปัจจุบันและคำนวณการหมุนเป้าหมายสำหรับการนอนแบน
        this.settleInitialRotationX = this.coinRotationX;
        this.settleInitialRotationY = this.coinRotationY;
        this.settleInitialRotationZ = this.coinRotationZ;
        this.settleInitialPositionY = this.coinPositionY;

        // คำนวณผลลัพธ์สุดท้ายเพื่อกำหนดการหมุนเป้าหมาย
        const finalResult = this.intendedResult || this.getCurrentResult();

        // กำหนดการหมุนเป้าหมายสำหรับการนอนแบนบนพื้น
        // ใช้การหมุนที่ทำให้เหรียญนอนแบนและแสดงหน้าที่ถูกต้อง
        //
        // ใน Three.js CylinderGeometry:
        // - Default orientation: Y-axis เป็นแกนหลัก (สูง)
        // - materials[1] = top face (+Y direction) = heads
        // - materials[2] = bottom face (-Y direction) = tails
        //
        // เพื่อให้เหรียญนอนแบนบนพื้น (XZ plane) และแสดงผลลัพธ์ถูกต้อง:

        if (finalResult === 'heads') {
            // หัว: หน้าบน (top face) หันขึ้น - เหรียญนอนแบนปกติ
            this.settleTargetRotationX = 0;        // ไม่หมุนรอบแกน X (เหรียญนอนแบน)
            this.settleTargetRotationY = 0;        // ไม่หมุนรอบแกน Y
            this.settleTargetRotationZ = 0;        // ไม่หมุนรอบแกน Z
        } else {
            // ก้อย: หน้าล่าง (bottom face) หันขึ้น - หมุนเหรียญ 180° รอบแกน X
            this.settleTargetRotationX = Math.PI;  // หมุน 180° รอบแกน X เพื่อให้หน้าล่างหันขึ้น
            this.settleTargetRotationY = 0;        // ไม่หมุนรอบแกน Y
            this.settleTargetRotationZ = 0;        // ไม่หมุนรอบแกน Z
        }

        // เก็บตำแหน่ง Y เป้าหมายสำหรับการนอนแบนบนพื้น
        // ตำแหน่งจุดศูนย์กลางของเหรียญ = ตำแหน่งพื้น + ครึ่งความสูงของเหรียญ
        this.settleTargetPositionY = this.groundY + this.coinHalfHeight;

        console.log('🎯 Starting settling animation:', {
            finalResult,
            initialRotation: {
                x: (this.settleInitialRotationX * 180 / Math.PI).toFixed(1) + '°',
                y: (this.settleInitialRotationY * 180 / Math.PI).toFixed(1) + '°',
                z: (this.settleInitialRotationZ * 180 / Math.PI).toFixed(1) + '°'
            },
            targetRotation: {
                x: (this.settleTargetRotationX * 180 / Math.PI).toFixed(1) + '°',
                y: (this.settleTargetRotationY * 180 / Math.PI).toFixed(1) + '°',
                z: (this.settleTargetRotationZ * 180 / Math.PI).toFixed(1) + '°'
            },
            initialPositionY: this.settleInitialPositionY.toFixed(3),
            targetPositionY: this.settleTargetPositionY.toFixed(3),
            rotationDifference: {
                x: ((this.settleTargetRotationX - this.settleInitialRotationX) * 180 / Math.PI).toFixed(1) + '°',
                y: ((this.settleTargetRotationY - this.settleInitialRotationY) * 180 / Math.PI).toFixed(1) + '°',
                z: ((this.settleTargetRotationZ - this.settleInitialRotationZ) * 180 / Math.PI).toFixed(1) + '°'
            }
        });
    }

    /**
     * อัพเดทการปรับตัวแบบค่อยๆ หยุด - ค่อยๆ หมุนไปสู่ตำแหน่งนอนแบนบนพื้น
     */
    updateSettlingAnimation() {
        const elapsed = Date.now() - this.settleStartTime;
        const settleDuration = 1500; // เพิ่มเป็น 1.5 วินาทีเพื่อให้การเปลี่ยนแปลงดูเรียบเนียนขึ้น

        if (elapsed < settleDuration) {
            const progress = elapsed / settleDuration;

            // ใช้ easing function เพื่อให้การเปลี่ยนแปลงดูเรียบเนียน
            const easedProgress = this.easeInOutCubic(progress);

            // ค่อยๆ หมุนจากตำแหน่งปัจจุบันไปสู่ตำแหน่งเป้าหมาย (นอนแบน)
            // ใช้ angular interpolation สำหรับการหมุนเพื่อให้ได้ค่าที่แม่นยำ
            this.coinRotationX = this.lerpAngle(this.settleInitialRotationX, this.settleTargetRotationX, easedProgress);
            this.coinRotationY = this.lerpAngle(this.settleInitialRotationY, this.settleTargetRotationY, easedProgress);
            this.coinRotationZ = this.lerpAngle(this.settleInitialRotationZ, this.settleTargetRotationZ, easedProgress);

            // ค่อยๆ ปรับตำแหน่ง Y ให้เหรียญลงสู่ตำแหน่งนอนแบนบนพื้น
            this.coinPositionY = this.lerp(this.settleInitialPositionY, this.settleTargetPositionY, easedProgress);

            // ลดการสั่นให้น้อยลงเพื่อไม่รบกวนการนอนแบน
            if (progress < 0.2) {
                const wobbleIntensity = (0.2 - progress) * 0.008; // ลดความแรงของการสั่น
                const wobbleFreq = 8; // ลดความถี่ของการสั่น

                // เพิ่มการสั่นเล็กน้อยในแกน Y เท่านั้น เพื่อไม่รบกวนการนอนแบน
                const wobbleY = Math.sin(elapsed * wobbleFreq * 0.01) * wobbleIntensity;
                this.coinRotationY += wobbleY;

                // เพิ่มการสั่นเล็กน้อยในตำแหน่ง Y เพื่อจำลองการกระเด้งเล็กน้อย
                const positionWobble = Math.sin(elapsed * wobbleFreq * 0.015) * wobbleIntensity * 0.01;
                this.coinPositionY += positionWobble;
            }

            // อัพเดทการหมุนและตำแหน่งของเหรียญ
            this.coin.rotation.x = this.coinRotationX;
            this.coin.rotation.y = this.coinRotationY;
            this.coin.rotation.z = this.coinRotationZ;
            this.coin.position.y = this.coinPositionY;

            // แสดงความคืบหน้าในการปรับตัว (สำหรับ debug) - ลดความถี่ลง
            if (elapsed % 300 < 16) { // แสดงทุก 300ms
                console.log('🔄 Settling progress:', {
                    progress: (progress * 100).toFixed(1) + '%',
                    targetRotation: {
                        x: (this.settleTargetRotationX * 180 / Math.PI).toFixed(1) + '°',
                        y: (this.settleTargetRotationY * 180 / Math.PI).toFixed(1) + '°',
                        z: (this.settleTargetRotationZ * 180 / Math.PI).toFixed(1) + '°'
                    },
                    currentRotation: {
                        x: (this.coinRotationX * 180 / Math.PI).toFixed(1) + '°',
                        y: (this.coinRotationY * 180 / Math.PI).toFixed(1) + '°',
                        z: (this.coinRotationZ * 180 / Math.PI).toFixed(1) + '°'
                    },
                    targetPositionY: this.settleTargetPositionY.toFixed(3),
                    currentPositionY: this.coinPositionY.toFixed(3)
                });
            }
        } else {
            // จบการปรับตัวและแสดงผลสุดท้าย
            this.isSettling = false;

            // ตั้งค่าสุดท้ายให้แน่นอนเพื่อให้เหรียญนอนแบนสมบูรณ์
            this.coinRotationX = this.settleTargetRotationX;
            this.coinRotationY = this.settleTargetRotationY;
            this.coinRotationZ = this.settleTargetRotationZ;
            this.coinPositionY = this.settleTargetPositionY;

            // *** สำคัญ: ตั้งค่าการหมุนและตำแหน่งสุดท้ายให้เหรียญนอนแบนบนพื้น ***
            this.coin.rotation.x = this.coinRotationX;
            this.coin.rotation.y = this.coinRotationY;
            this.coin.rotation.z = this.coinRotationZ;
            this.coin.position.y = this.coinPositionY;

            console.log('✅ Settling animation completed - coin should be flat now');
            console.log('🎯 Final settling values:', {
                targetRotation: {
                    x: (this.settleTargetRotationX * 180 / Math.PI).toFixed(1) + '°',
                    y: (this.settleTargetRotationY * 180 / Math.PI).toFixed(1) + '°',
                    z: (this.settleTargetRotationZ * 180 / Math.PI).toFixed(1) + '°'
                },
                actualRotation: {
                    x: (this.coin.rotation.x * 180 / Math.PI).toFixed(1) + '°',
                    y: (this.coin.rotation.y * 180 / Math.PI).toFixed(1) + '°',
                    z: (this.coin.rotation.z * 180 / Math.PI).toFixed(1) + '°'
                },
                targetPositionY: this.settleTargetPositionY.toFixed(3),
                actualPositionY: this.coin.position.y.toFixed(3),
                isFlat: Math.abs(this.coin.rotation.x) < 0.01 || Math.abs(this.coin.rotation.x - Math.PI) < 0.01
            });

            this.finalizeCoinResult();
        }
    }

    /**
     * เริ่ม idle animation - แอนิเมชันหมุนเบาๆ เมื่อไม่ได้ใช้งาน
     */
    startIdle() {
        console.log('✅ Starting idle animation');

        // ทำความสะอาด win/lose scene elements ก่อน
        this.cleanupWinLoseScene();

        // รีเซ็ตเหรียญให้ตั้งขึ้นก่อนเริ่มหมุน
        this.resetCoinToVertical();

        // ตั้งค่าสถานะ idle
        this.isIdle = true;
        this.isFlipping = false;

        // ตั้งค่ากล้องสำหรับ idle state
        this.setCameraToIdleState();
    }

    /**
     * รีเซ็ตเหรียญให้อยู่ในตำแหน่งตั้งขึ้น (vertical position)
     * ใช้สำหรับเตรียมเหรียญก่อนเริ่ม idle animation
     */
    resetCoinToVertical() {
        // === รีเซ็ตเหรียญให้ตั้งขึ้น (ยืนบนขอบ) ===
        this.coin.scale.set(1, 1, 1);                    // ขนาดปกติ
        this.coin.position.set(0, 0, 0);                 // ตำแหน่งกึ่งกลาง
        this.coin.rotation.set(Math.PI / 2, 0, 0);       // หมุน 90 องศารอบแกน X เพื่อให้ตั้งขึ้น

        // === รีเซ็ตสถานะภายในให้ตรงกับตำแหน่งตั้งขึ้น ===
        this.coinRotationX = Math.PI / 2;                // 90 องศาในหน่วย radian
        this.coinRotationY = 0;                          // ไม่หมุนรอบแกน Y
        this.coinRotationZ = 0;                          // ไม่หมุนรอบแกน Z
        this.coinPositionY = 0;                          // ตำแหน่งกึ่งกลาง
        this.velocity = { x: 0, y: 0, z: 0 };           // ไม่มีความเร็ว
        this.angularVelocity = { x: 0, y: 0, z: 0 };    // ไม่มีความเร็วเชิงมุม
        this.bounceCount = 0;                            // รีเซ็ตจำนวนการกระเด้ง
        this.isSettling = false;                         // รีเซ็ตสถานะการปรับตัว

        // === รีเซ็ตความเข้มของแสง ambient ===
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }

        // Remove final result light if it exists
        if (this.finalResultLight) {
            this.scene.remove(this.finalResultLight);
            this.scene.remove(this.finalResultLight.target);
            this.finalResultLight = null;
        }

        console.log('🔄 Coin reset to vertical position for idle animation');
    }

    /**
     * หยุด idle animation
     */
    stopIdle() {
        this.isIdle = false;
    }

    /**
     * ทอยเหรียญ - เมธอดหลักสำหรับเริ่มแอนิเมชันการโยนเหรียญ
     * @param {string|null} result - ผลลัพธ์ที่ต้องการ ('heads', 'tails', หรือ null สำหรับสุ่ม)
     * @param {string|null} winLoseState - สถานะ win/lose ('win', 'lose', หรือ null สำหรับไม่แสดง scene)
     * @returns {Promise<string>} - Promise ที่ resolve เมื่อการโยนเสร็จสิ้น พร้อมผลลัพธ์
     */
    async flipCoin(result = null, winLoseState = null) {
        // ตรวจสอบว่าไม่ได้อยู่ในระหว่างการโยนอยู่
        if (this.isFlipping) return;

        // === ตั้งค่าสถานะการโยน ===
        this.isFlipping = true;           // เริ่มการโยน
        this.isIdle = false;              // หยุด idle animation
        this.bounceCount = 0;             // รีเซ็ตจำนวนการกระเด้ง
        this.isSettling = false;          // รีเซ็ตสถานะการปรับตัว
        this.cameraStoppedTracking = false; // รีเซ็ตสถานะการติดตามของกล้อง

        // เก็บผลลัพธ์ที่ต้องการไว้ใช้ในการคำนวณ
        this.intendedResult = typeof result === 'string' ? result.toLowerCase() : result;

        // กำหนดผลลัพธ์สุดท้ายถ้ายังไม่ได้กำหนด
        const finalResult = this.intendedResult || (Math.random() < 0.5 ? 'heads' : 'tails');

        // เลือกจำนวนการ bounce ตามผลลัพธ์
        this.targetBounces = this.selectBounceCount(finalResult);

        // เลือกความสูงและความเร็วหมุนตามผลลัพธ์
        const heightAndSpeed = this.selectHeightAndRotationSpeed(finalResult);
        this.maxFlipHeight = heightAndSpeed.height;
        this.flipRotationSpeed = heightAndSpeed.rotationSpeed;

        console.log('🎯 Flip Test Configuration:', {
            intendedResult: this.intendedResult,
            finalResult: finalResult,
            targetBounces: this.targetBounces,
            maxFlipHeight: this.maxFlipHeight,
            flipRotationSpeed: this.flipRotationSpeed,
            currentSettingsPatterns: this.options.settingsPatterns
        });

        // เก็บสถานะ win/lose ไว้ใช้ในการแสดงผล
        this.winLoseState = winLoseState;

        // === เปลี่ยนกล้องไปสู่ flipping state ===
        this.transitionCameraToFlippingState();

        // รีเซ็ตขนาดเหรียญกลับสู่ขนาดเริ่มต้น
        this.coin.scale.set(1, 1, 1);

        // === รีเซ็ตตำแหน่งและการหมุนสำหรับการโยนใหม่ ===
        // ตั้งค่าตำแหน่งเหรียญให้อยู่ตรงกลางแกน X และ Z เสมอ
        this.coin.position.set(0, 0, 0);
        this.coinPositionY = 0;  // ตำแหน่งเริ่มต้นที่กึ่งกลาง

        // === ตั้งค่าการหมุนเริ่มต้นให้หน้าผลลัพธ์อยู่ด้านบน ===
        // ตั้งค่าการหมุนเริ่มต้นให้หน้าที่เป็นผลลัพธ์อยู่ด้านบน (หงายขึ้น)
        if (finalResult === 'heads') {
            // หัว: หน้า heads (material[1]) อยู่ด้านบน
            this.coinRotationX = 0;  // หน้า heads อยู่ด้านบน
        } else {
            // ก้อย: หน้า tails (material[2]) อยู่ด้านบน
            this.coinRotationX = Math.PI;  // หมุน 180 องศาให้หน้า tails อยู่ด้านบน
        }
        this.coinRotationY = 0;  // ไม่มีการหมุนรอบแกน Y
        this.coinRotationZ = 0;  // ไม่มีการหมุนรอบแกน Z

        // === อัพเดทการหมุนจริงของเหรียญใน Three.js ===
        this.coin.rotation.set(this.coinRotationX, this.coinRotationY, this.coinRotationZ);

        console.log('🪙 Coin reset to show result face up:', {
            finalResult: finalResult,
            coinRotationX: this.coinRotationX,
            coinRotationXDegrees: (this.coinRotationX * 180 / Math.PI).toFixed(1) + '°'
        });

        // === ตั้งค่าระบบ Physics แบบใหม่ ===
        // รีเซ็ตสถานะการโยน
        this.flipPhase = 'ascending';        // เริ่มต้นด้วยช่วงขึ้น
        this.hasReachedPeak = false;         // ยังไม่ถึงจุดสูงสุด

        // ตั้งค่าความเร็วเริ่มต้น (ใช้เฉพาะในช่วงลง)
        this.velocity.x = 0;
        this.velocity.y = 0; // ไม่ใช้ velocity ในช่วงขึ้น
        this.velocity.z = 0;

        // === คำนวณการหมุนเป้าหมายตามผลลัพธ์ที่ต้องการ (แกนเดียว) ===
        // เพิ่มจำนวนรอบการหมุนให้เร็วมากขึ้นเพื่อให้ยากต่อการคาดเดา
        const baseRotations = 12 + Math.random() * 6; // สุ่มระหว่าง 12-18 รอบ

        // คำนวณการหมุนรอบแกน X (end-over-end flip แบบธรรมชาติ)
        // ไม่ใช้ randomOffset เพื่อให้ผลลัพธ์แน่นอน
        let targetRotationX;
        if (this.intendedResult === 'heads') {
            // หัว: หมุนเป็นจำนวนรอบเต็ม (จบที่ 0 หรือ 2π)
            targetRotationX = baseRotations * Math.PI * 2;
        } else if (this.intendedResult === 'tails') {
            // ก้อย: หมุนเป็นจำนวนรอบเต็ม + ครึ่งรอบ (จบที่ π)
            targetRotationX = (baseRotations * Math.PI * 2) + Math.PI;
        } else {
            // สุ่ม: เลือกระหว่าง heads หรือ tails
            const randomResult = Math.random() < 0.5 ? 'heads' : 'tails';
            this.intendedResult = randomResult;
            targetRotationX = randomResult === 'heads'
                ? baseRotations * Math.PI * 2
                : (baseRotations * Math.PI * 2) + Math.PI;
        }

        console.log('🎯 Target rotation calculated:', {
            intendedResult: this.intendedResult,
            baseRotations: baseRotations.toFixed(1),
            targetRotationX: targetRotationX.toFixed(3) + ' rad (' + (targetRotationX * 180 / Math.PI).toFixed(1) + '°)',
            finalExpectedRotation: ((targetRotationX % (Math.PI * 2)) * 180 / Math.PI).toFixed(1) + '°'
        });

        // === ตั้งค่าการหมุนแบบ Deterministic ===
        // เก็บค่าเป้าหมายและเวลาเริ่มต้นสำหรับการคำนวณการหมุนที่แน่นอน
        this.targetRotationX = targetRotationX;
        this.initialRotationX = this.coinRotationX; // ใช้ค่าการหมุนเริ่มต้นที่ตั้งไว้ข้างบน
        this.flipStartTime = Date.now();

        // ไม่ใช้ angularVelocity แบบเดิมอีกต่อไป - ใช้การคำนวณตามเวลาแทน
        this.angularVelocity.x = 0;
        this.angularVelocity.y = 0;
        this.angularVelocity.z = 0;

        // === คืนค่า Promise ที่จะ resolve เมื่อแอนิเมชันเสร็จสิ้น ===
        return new Promise((resolve) => {
            this.flipResolve = resolve; // เก็บ resolve function ไว้เรียกใน finish scene
        });
    }

    /**
     * จบการโยนเหรียญ (ไม่ใช้แล้ว - ใช้ forceFlat() แทน)
     * หมายเหตุ: ฟังก์ชันนี้ไม่ถูกเรียกแล้วเพราะใช้ forceFlat() ทันทีเมื่อหยุดกระเด้ง
     */
    finalizeCoinResult() {
        console.log('⚠️ finalizeCoinResult() called but should use forceFlat() instead');
    }

    /**
     * เพิ่มเอฟเฟกต์เพื่อเน้นผลลัพธ์สุดท้าย
     * ทำให้เหรียญดูชัดเจนและโดดเด่นขึ้นเมื่อแสดงผลลัพธ์แบบนอนแบน
     * @param {string|null} winLoseState - 'win', 'lose', หรือ null สำหรับแสดงผลปกติ
     */
    enhanceFinalResult(winLoseState = null) {
        // เพิ่มความสว่างของแสง ambient เล็กน้อยเพื่อเน้นผลลัพธ์
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.9; // เพิ่มเป็น 0.9 เพื่อให้เห็นหน้าเหรียญชัดเจนขึ้น
        }

        // เพิ่มแสงจุดเพื่อเน้นเหรียญที่นอนแบน
        const spotLight = new THREE.SpotLight(0xffffff, 0.5, 10, Math.PI / 6, 0.1);
        spotLight.position.set(0, 2, 0);
        spotLight.target.position.set(0, this.groundY, 0);
        spotLight.castShadow = false; // ไม่ต้องการเงาเพิ่มเติม
        this.scene.add(spotLight);
        this.scene.add(spotLight.target);

        // เก็บ reference เพื่อลบทีหลัง
        this.finalResultLight = spotLight;

        // เพิ่มเอฟเฟกต์ pulse เล็กน้อยเพื่อดึงดูดความสนใจ
        const originalScale = this.coin.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(1.08); // เพิ่มขนาดเล็กน้อย

        // Animate scale up and down for emphasis
        const duration = 600;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            let scale;
            if (progress < 0.5) {
                // Scale up
                const t = progress * 2;
                scale = originalScale.clone().lerp(targetScale, t);
            } else {
                // Scale down
                const t = (progress - 0.5) * 2;
                scale = targetScale.clone().lerp(originalScale, t);
            }

            this.coin.scale.copy(scale);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.coin.scale.copy(originalScale);
                // แสดง win/lose scene หลังจาก pulse animation เสร็จ
                if (winLoseState) {
                    this.showWinLoseScene(winLoseState);
                }
            }
        };

        animate();
    }

    /**
     * แสดง Win/Lose Scene พร้อมข้อความและเอฟเฟกต์
     * @param {string} winLoseState - 'win' หรือ 'lose'
     */
    showWinLoseScene(winLoseState) {
        console.log('🎊 Showing win/lose scene:', winLoseState);

        // สร้างข้อความ WIN!!! หรือ LOSE
        this.createWinLoseText(winLoseState);

        // เล่นเอฟเฟกต์ confetti เฉพาะเมื่อชนะ
        if (winLoseState === 'win') {
            this.createConfettiEffect();
        }
    }

    /**
     * สร้างข้อความ WIN!!! หรือ LOSE แสดงเหนือเหรียญ
     * @param {string} winLoseState - 'win' หรือ 'lose'
     */
    createWinLoseText(winLoseState) {
        // สร้าง canvas สำหรับข้อความ
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 128;
        const ctx = canvas.getContext('2d');

        // ตั้งค่าข้อความและสี
        const isWin = winLoseState === 'win';
        const text = isWin ? 'WIN!!!' : 'LOSE';
        const color = isWin ? '#00ff00' : '#ff0000'; // เขียวสำหรับชนะ, แดงสำหรับแพ้

        // วาดพื้นหลังโปร่งใส
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // ตั้งค่าฟอนต์และสไตล์
        ctx.font = 'bold 80px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // วาดเงาข้อความ
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillText(text, canvas.width / 2 + 3, canvas.height / 2 + 3);

        // วาดข้อความหลัก
        ctx.fillStyle = color;
        ctx.fillText(text, canvas.width / 2, canvas.height / 2);

        // สร้าง texture จาก canvas
        const texture = new THREE.CanvasTexture(canvas);
        texture.needsUpdate = true;

        // สร้าง material สำหรับข้อความ
        const material = new THREE.MeshBasicMaterial({
            map: texture,
            transparent: true,
            alphaTest: 0.1,
            side: THREE.DoubleSide
        });

        // สร้าง geometry สำหรับข้อความ
        const geometry = new THREE.PlaneGeometry(4, 1); // ขนาดข้อความ

        // สร้าง mesh สำหรับข้อความ
        const textMesh = new THREE.Mesh(geometry, material);

        // วางตำแหน่งข้อความเหนือเหรียญ
        const coinY = this.groundY + this.coinHalfHeight;
        textMesh.position.set(0, coinY + 1.5, 0); // อยู่เหนือเหรียญ 1.5 หน่วย

        // หมุนข้อความให้หันหากล้อง
        textMesh.lookAt(this.camera.position);

        // เพิ่มข้อความเข้าไปใน scene
        this.scene.add(textMesh);

        // เก็บ reference เพื่อลบทีหลัง
        this.winLoseText = textMesh;

        // เพิ่มแอนิเมชันปรากฏขึ้น
        this.animateTextAppearance(textMesh);

        console.log('📝 Win/lose text created:', text, 'at position:', textMesh.position);
    }

    /**
     * แอนิเมชันการปรากฏของข้อความ WIN/LOSE
     * @param {THREE.Mesh} textMesh - mesh ของข้อความ
     */
    animateTextAppearance(textMesh) {
        const startScale = 0.1;
        const targetScale = 1.0;
        const duration = 800;
        const startTime = Date.now();

        // เริ่มต้นด้วยขนาดเล็ก
        textMesh.scale.setScalar(startScale);

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // ใช้ ease-out-back เพื่อให้มีเอฟเฟกต์ bounce
            const easedProgress = this.easeOutBack(progress);
            const currentScale = startScale + (targetScale - startScale) * easedProgress;

            textMesh.scale.setScalar(currentScale);

            // ปรับความโปร่งใสด้วย
            textMesh.material.opacity = progress;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    /**
     * สร้างเอฟเฟกต์ confetti สำหรับการชนะ - Music Festival Style
     * พุ่งจากด้านล่างเหมือนพุเวที music festival พร้อมเอฟเฟกต์อลังการ
     */
    createConfettiEffect() {
        console.log('🎊 Creating EPIC confetti effect - Music Festival Style!');

        const confettiParticles = [];
        const particleCount = 2000; // เพิ่มจำนวนอนุภาค

        // สีสันสดใสแบบ music festival
        const festivalColors = [
            0xff1493, // Deep Pink
            0x00ff7f, // Spring Green
            0xff4500, // Orange Red
            0x1e90ff, // Dodger Blue
            0xffd700, // Gold
            0xff69b4, // Hot Pink
            0x00ced1, // Dark Turquoise
            0xff6347, // Tomato
            0x9370db, // Medium Purple
            0x32cd32, // Lime Green
            0xff1493, // Deep Pink (duplicate for higher chance)
            0xffa500  // Orange
        ];

        // สร้าง confetti launchers จากด้านล่าง (เหมือนพุเวที)
        const launcherPositions = [
            { x: -3, z: -3 }, // มุมซ้ายหลัง
            { x: 3, z: -3 },  // มุมขวาหลัง
            { x: -3, z: 3 },  // มุมซ้ายหน้า
            { x: 3, z: 3 },   // มุมขวาหน้า
            { x: 0, z: -4 },  // กลางหลัง
            { x: 0, z: 4 },   // กลางหน้า
            { x: -4, z: 0 },  // กลางซ้าย
            { x: 4, z: 0 }    // กลางขวา
        ];

        // สร้างอนุภาคสำหรับแต่ละ launcher
        const particlesPerLauncher = Math.floor(particleCount / launcherPositions.length);

        launcherPositions.forEach((launcher, launcherIndex) => {
            for (let i = 0; i < particlesPerLauncher; i++) {
                // สร้างรูปทรงที่หลากหลาย
                let geometry;
                const shapeType = Math.random();
                if (shapeType < 0.4) {
                    // กระดาษสี่เหลี่ยม (40%)
                    geometry = new THREE.BoxGeometry(
                        0.08 + Math.random() * 0.12, // ความกว้าง
                        0.08 + Math.random() * 0.12, // ความสูง
                        0.01 + Math.random() * 0.02  // ความหนา
                    );
                } else if (shapeType < 0.7) {
                    // วงกลม/ดาว (30%)
                    geometry = new THREE.CylinderGeometry(
                        0.05 + Math.random() * 0.08, // รัศมีบน
                        0.05 + Math.random() * 0.08, // รัศมีล่าง
                        0.01 + Math.random() * 0.02, // ความสูง
                        6 + Math.floor(Math.random() * 6) // จำนวนด้าน (6-12)
                    );
                } else {
                    // รูปทรงสามเหลี่ยม (30%)
                    geometry = new THREE.ConeGeometry(
                        0.06 + Math.random() * 0.08, // รัศมี
                        0.08 + Math.random() * 0.12, // ความสูง
                        3 // สามเหลี่ยม
                    );
                }

                // วัสดุที่มีความเงางาม
                const material = new THREE.MeshPhongMaterial({
                    color: festivalColors[Math.floor(Math.random() * festivalColors.length)],
                    shininess: 100,
                    transparent: true,
                    opacity: 0.9
                });

                const particle = new THREE.Mesh(geometry, material);

                // ตำแหน่งเริ่มต้นจากพื้น (เหมือนพุเวที)
                const groundY = this.groundY;
                const spreadRadius = 0.3 + Math.random() * 0.5; // กระจายรอบ launcher
                const angle = Math.random() * Math.PI * 2;

                particle.position.set(
                    launcher.x + Math.cos(angle) * spreadRadius,
                    groundY + 0.1, // เริ่มจากพื้น
                    launcher.z + Math.sin(angle) * spreadRadius
                );

                // ความเร็วเริ่มต้นแบบพุ่งขึ้น (เหมือนพุเวที)
                const launchPower = 0.15 + Math.random() * 0.25; // พลังพุ่ง
                const launchAngle = Math.PI * 0.3 + Math.random() * Math.PI * 0.4; // มุมพุ่ง 30-70 องศา
                const horizontalAngle = Math.atan2(launcher.z, launcher.x) + (Math.random() - 0.5) * 0.8; // ทิศทางไปยังกลาง

                particle.userData = {
                    velocity: {
                        x: Math.cos(horizontalAngle) * Math.cos(launchAngle) * launchPower,
                        y: Math.sin(launchAngle) * launchPower, // พุ่งขึ้นแรงๆ
                        z: Math.sin(horizontalAngle) * Math.cos(launchAngle) * launchPower
                    },
                    angularVelocity: {
                        x: (Math.random() - 0.5) * 0.4,
                        y: (Math.random() - 0.5) * 0.4,
                        z: (Math.random() - 0.5) * 0.4
                    },
                    life: 1.0,
                    initialDelay: launcherIndex * 50 + i * 2, // ดีเลย์การเปิดตัวแบบลูกคลื่น
                    launcherIndex: launcherIndex,
                    airResistance: 0.995 + Math.random() * 0.004, // ความต้านอากาศ
                    sparklePhase: Math.random() * Math.PI * 2, // เฟสการเปล่งประกาย
                    originalColor: material.color.clone()
                };

                this.scene.add(particle);
                confettiParticles.push(particle);
            }
        });

        // เก็บ reference เพื่อลบทีหลัง
        this.confettiParticles = confettiParticles;
        this.confettiStartTime = Date.now();

        // เพิ่มแสงไฟพิเศษสำหรับ confetti effect
        this.createConfettiLighting();

        // เริ่มแอนิเมชัน confetti
        this.animateConfetti();
    }

    /**
     * สร้างแสงไฟพิเศษสำหรับ confetti effect
     */
    createConfettiLighting() {
        // แสงสีสันสดใสที่เปลี่ยนสี
        const coloredLights = [
            { color: 0xff1493, position: new THREE.Vector3(-2, 1, -2) }, // Deep Pink
            { color: 0x00ff7f, position: new THREE.Vector3(2, 1, -2) },  // Spring Green
            { color: 0x1e90ff, position: new THREE.Vector3(-2, 1, 2) },  // Dodger Blue
            { color: 0xffd700, position: new THREE.Vector3(2, 1, 2) }    // Gold
        ];

        this.confettiLights = [];

        coloredLights.forEach((lightData, index) => {
            const light = new THREE.PointLight(lightData.color, 0.8, 8);
            light.position.copy(lightData.position);

            // เก็บข้อมูลสำหรับแอนิเมชัน
            light.userData = {
                originalColor: new THREE.Color(lightData.color),
                phase: index * Math.PI * 0.5, // เฟสที่ต่างกัน
                intensity: 0.1,
                originalPosition: lightData.position.clone()
            };

            this.scene.add(light);
            this.confettiLights.push(light);
        });

        // แสงกลางที่เปล่งประกายแรง
        const centerLight = new THREE.PointLight(0xffffff, 1.2, 6);
        centerLight.position.set(0, 0.5, 0);
        centerLight.userData = {
            originalColor: new THREE.Color(0xffffff),
            phase: 0,
            intensity: 0.2,
            originalPosition: new THREE.Vector3(0, 0.5, 0)
        };

        this.scene.add(centerLight);
        this.confettiLights.push(centerLight);

        console.log('✨ Confetti lighting effects created');
    }

    /**
     * แอนิเมชัน confetti particles - Music Festival Style
     * พร้อมเอฟเฟกต์ฟิสิกส์ที่สมจริงและการเปล่งประกาย
     */
    animateConfetti() {
        if (!this.confettiParticles || this.confettiParticles.length === 0) return;

        const gravity = -0.012; // แรงโน้มถ่วงที่แรงขึ้น
        const fadeSpeed = 0.003; // ค่อยๆ จางลงช้าขึ้น
        const windForce = 0.002; // แรงลม

        const animate = () => {
            const currentTime = Date.now();
            const elapsedTime = currentTime - this.confettiStartTime;
            let activeParticles = 0;

            this.confettiParticles.forEach((particle, index) => {
                // ตรวจสอบดีเลย์การเปิดตัว
                if (elapsedTime < particle.userData.initialDelay) {
                    particle.visible = false;
                    activeParticles++;
                    return;
                }

                if (particle.userData.life <= 0) return;

                // เริ่มแสดงอนุภาค
                particle.visible = true;

                // === ฟิสิกส์ที่สมจริง ===

                // แรงโน้มถ่วง
                particle.userData.velocity.y += gravity;

                // ความต้านอากาศ (ทำให้อนุภาคค่อยๆ ช้าลง)
                particle.userData.velocity.x *= particle.userData.airResistance;
                particle.userData.velocity.z *= particle.userData.airResistance;

                // แรงลมแบบสุ่ม (สร้างการเคลื่อนไหวที่เป็นธรรมชาติ)
                const windX = (Math.sin(elapsedTime * 0.001 + index * 0.1) * windForce);
                const windZ = (Math.cos(elapsedTime * 0.0008 + index * 0.15) * windForce);
                particle.userData.velocity.x += windX;
                particle.userData.velocity.z += windZ;

                // อัพเดทตำแหน่ง
                particle.position.x += particle.userData.velocity.x;
                particle.position.y += particle.userData.velocity.y;
                particle.position.z += particle.userData.velocity.z;

                // การเด้งเมื่อกระทบพื้น
                if (particle.position.y <= this.groundY + 0.05) {
                    particle.position.y = this.groundY + 0.05;
                    particle.userData.velocity.y *= -0.3; // เด้งกลับแต่สูญเสียพลังงาน
                    particle.userData.velocity.x *= 0.8; // ลดความเร็วแนวนอน
                    particle.userData.velocity.z *= 0.8;
                }

                // === การหมุนที่สมจริง ===
                // ความต้านอากาศส่งผลต่อการหมุนด้วย
                particle.userData.angularVelocity.x *= 0.995;
                particle.userData.angularVelocity.y *= 0.995;
                particle.userData.angularVelocity.z *= 0.995;

                particle.rotation.x += particle.userData.angularVelocity.x;
                particle.rotation.y += particle.userData.angularVelocity.y;
                particle.rotation.z += particle.userData.angularVelocity.z;

                // === เอฟเฟกต์การเปล่งประกาย ===
                particle.userData.sparklePhase += 0.1;
                const sparkleIntensity = (Math.sin(particle.userData.sparklePhase) + 1) * 0.5;

                // เปลี่ยนสีแบบไดนามิก
                const originalColor = particle.userData.originalColor;
                const sparkleColor = new THREE.Color().copy(originalColor);
                sparkleColor.multiplyScalar(0.7 + sparkleIntensity * 0.6); // เปล่งประกาย
                particle.material.color.copy(sparkleColor);

                // === การจางหาย ===
                particle.userData.life -= fadeSpeed;

                // ความโปร่งใสแบบ smooth curve
                const lifeRatio = particle.userData.life;
                let opacity;
                if (lifeRatio > 0.7) {
                    // ช่วงแรก: ค่อยๆ ปรากฏ
                    opacity = (1 - lifeRatio) / 0.3;
                } else if (lifeRatio > 0.3) {
                    // ช่วงกลาง: ความโปร่งใสเต็ม
                    opacity = 0.9;
                } else {
                    // ช่วงท้าย: ค่อยๆ จางหาย
                    opacity = lifeRatio / 0.3 * 0.9;
                }

                particle.material.opacity = Math.max(0, opacity);

                // ซ่อนอนุภาคที่หมดชีวิต
                if (particle.userData.life > 0) {
                    activeParticles++;
                }
            });

            // === อัพเดทแสงไฟ confetti ===
            this.updateConfettiLighting(elapsedTime);

            // ถ้ายังมีอนุภาคที่ยังมีชีวิต ให้ทำแอนิเมชันต่อ
            if (activeParticles > 0) {
                requestAnimationFrame(animate);
            } else {
                // ลบอนุภาคทั้งหมดเมื่อหมดชีวิต
                this.cleanupConfetti();
            }
        };

        animate();
    }

    /**
     * อัพเดทแสงไฟ confetti ให้เปลี่ยนสีและเคลื่อนไหว
     */
    updateConfettiLighting(elapsedTime) {
        if (!this.confettiLights) return;

        this.confettiLights.forEach((light, index) => {
            const userData = light.userData;

            // อัพเดทเฟส
            userData.phase += 0.05;

            // เปลี่ยนสี (rainbow effect)
            const hue = (elapsedTime * 0.001 + index * 0.2) % 1;
            const saturation = 0.8 + Math.sin(userData.phase) * 0.2;
            const lightness = 0.6 + Math.sin(userData.phase * 1.5) * 0.3;

            light.color.setHSL(hue, saturation, lightness);

            // เปลี่ยนความเข้ม (pulsing effect)
            const intensityMultiplier = 0.7 + Math.sin(userData.phase * 2) * 0.5;
            light.intensity = userData.intensity * intensityMultiplier;

            // เคลื่อนไหวเล็กน้อย (floating effect)
            const floatY = Math.sin(userData.phase * 0.8) * 0.3;
            const floatX = Math.cos(userData.phase * 0.6) * 0.2;

            light.position.x = userData.originalPosition.x + floatX;
            light.position.y = userData.originalPosition.y + floatY;
        });
    }

    /**
     * ทำความสะอาด confetti particles และแสงไฟ
     */
    cleanupConfetti() {
        // ลบ confetti particles
        if (this.confettiParticles) {
            this.confettiParticles.forEach(particle => {
                this.scene.remove(particle);
                particle.geometry.dispose();
                particle.material.dispose();
            });
            this.confettiParticles = null;
        }

        // ลบแสงไฟ confetti
        if (this.confettiLights) {
            this.confettiLights.forEach(light => {
                this.scene.remove(light);
            });
            this.confettiLights = null;
        }

        console.log('🧹 Confetti particles and lighting effects cleaned up');
    }

    /**
     * ทำความสะอาด win/lose scene elements ทั้งหมด
     */
    cleanupWinLoseScene() {
        // ลบข้อความ WIN/LOSE
        if (this.winLoseText) {
            this.scene.remove(this.winLoseText);
            this.winLoseText.geometry.dispose();
            this.winLoseText.material.dispose();
            if (this.winLoseText.material.map) {
                this.winLoseText.material.map.dispose();
            }
            this.winLoseText = null;
            console.log('🧹 Win/lose text cleaned up');
        }

        // ลบ confetti particles
        this.cleanupConfetti();

        // ลบแสงเพิ่มเติม
        if (this.finalResultLight) {
            this.scene.remove(this.finalResultLight);
            this.scene.remove(this.finalResultLight.target);
            this.finalResultLight = null;
            console.log('🧹 Final result light cleaned up');
        }
    }

    /**
     * Ease-out-back function สำหรับเอฟเฟกต์ bounce
     * @param {number} t - progress (0-1)
     * @returns {number} - eased value
     */
    easeOutBack(t) {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
    }

    // หมายเหตุ: startFinishScene() ถูกลบออกแล้ว - ใช้ finalizeCoinResult() แทน

    // หมายเหตุ: updateFinishScene() ถูกลบออกแล้ว - ใช้ finalizeCoinResult() แทน

    /**
     * Easing function สำหรับแอนิเมชันที่เรียบเนียน
     * ใช้ cubic easing (เริ่มช้า -> เร็ว -> ช้า) เพื่อให้การเคลื่อนไหวดูธรรมชาติ
     * @param {number} t - ค่า progress ระหว่าง 0-1
     * @returns {number} - ค่าที่ผ่าน easing แล้ว
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Linear interpolation helper - คำนวณค่าระหว่างจุดเริ่มต้นและจุดสิ้นสุด
     * @param {number} start - ค่าเริ่มต้น
     * @param {number} end - ค่าสิ้นสุด
     * @param {number} t - ตำแหน่งระหว่าง 0-1 (0=start, 1=end)
     * @returns {number} - ค่าที่คำนวณได้
     */
    lerp(start, end, t) {
        return start + (end - start) * t;
    }

    /**
     * Angular interpolation helper - คำนวณการหมุนระหว่างสองมุมโดยใช้เส้นทางที่สั้นที่สุด
     * @param {number} start - มุมเริ่มต้น (radians)
     * @param {number} end - มุมสิ้นสุด (radians)
     * @param {number} t - ตำแหน่งระหว่าง 0-1 (0=start, 1=end)
     * @returns {number} - มุมที่คำนวณได้
     */
    lerpAngle(start, end, t) {
        // สำหรับการนอนแบน เราต้องการให้เหรียญหมุนไปยังตำแหน่งที่แน่นอน
        // ไม่ใช้เส้นทางที่สั้นที่สุด แต่ใช้การหมุนตรงไปยังเป้าหมาย

        // หากเป้าหมายคือ 0 หรือ π ให้หมุนตรงไปยังเป้าหมาย
        if (end === 0 || end === Math.PI) {
            return this.lerp(start, end, t);
        }

        // สำหรับกรณีอื่นๆ ใช้ angular interpolation ปกติ
        let diff = end - start;

        // หาเส้นทางที่สั้นที่สุด
        if (diff > Math.PI) {
            diff -= Math.PI * 2;
        } else if (diff < -Math.PI) {
            diff += Math.PI * 2;
        }

        return start + diff * t;
    }

    /**
     * Normalize rotation angle to find the shortest path between two angles
     * ปรับมุมการหมุนให้หาเส้นทางที่สั้นที่สุดระหว่างสองมุม
     * @param {number} start - มุมเริ่มต้น (radians)
     * @param {number} end - มุมสิ้นสุด (radians)
     * @returns {number} - มุมสิ้นสุดที่ปรับแล้วเพื่อให้เส้นทางสั้นที่สุด
     */
    normalizeRotationPath(start, end) {
        // ทำให้มุมอยู่ในช่วง 0 ถึง 2π
        const normalizeAngle = (angle) => {
            while (angle < 0) angle += Math.PI * 2;
            while (angle >= Math.PI * 2) angle -= Math.PI * 2;
            return angle;
        };

        const normalizedStart = normalizeAngle(start);
        const normalizedEnd = normalizeAngle(end);

        // คำนวณความแตกต่างของมุม
        let diff = normalizedEnd - normalizedStart;

        // หาเส้นทางที่สั้นที่สุด
        if (diff > Math.PI) {
            diff -= Math.PI * 2;
        } else if (diff < -Math.PI) {
            diff += Math.PI * 2;
        }

        return normalizedStart + diff;
    }

    /**
     * ตรวจสอบผลลัพธ์ปัจจุบันของเหรียญ (สำหรับระบบแกนเดียว)
     * วิเคราะห์จากการหมุนรอบแกน X เพื่อดูว่าหน้าไหนหันขึ้น
     * @returns {string} - 'heads' หรือ 'tails'
     */
    getCurrentResult() {
        // ทำให้การหมุน X อยู่ในช่วง 0 ถึง 2π
        const normalizedRotation = ((this.coinRotationX % (Math.PI * 2)) + (Math.PI * 2)) % (Math.PI * 2);

        // ในระบบแกนเดียว:
        // - การหมุน 0 หรือใกล้ 0 = หัว (heads) หันขึ้น
        // - การหมุน π หรือใกล้ π = ก้อย (tails) หันขึ้น
        // ใช้ threshold 0.5 รอบ π เพื่อความแม่นยำ
        const threshold = Math.PI / 2;

        if (normalizedRotation < threshold || normalizedRotation > (2 * Math.PI - threshold)) {
            return 'heads'; // ใกล้ 0 หรือ 2π
        } else if (normalizedRotation > (Math.PI - threshold) && normalizedRotation < (Math.PI + threshold)) {
            return 'tails'; // ใกล้ π
        } else {
            // สำหรับกรณีที่อยู่ระหว่างกลาง ให้ดูว่าใกล้ 0 หรือ π มากกว่า
            return normalizedRotation < Math.PI ? 'heads' : 'tails';
        }
    }

    // ทำลาย renderer
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.renderer) {
            this.renderer.dispose();
        }
    }

    // Method to reset coin to initial state (useful for game reset)
    resetCoin() {
        this.isFlipping = false;
        this.isIdle = false;

        // Reset camera to idle state
        this.setCameraToIdleState();

        // Reset coin to original state
        this.coin.scale.set(1, 1, 1);
        this.coin.position.set(0, 0, 0);
        this.coin.rotation.set(0, 0, 0);

        // Reset internal state
        this.coinRotationX = 0;
        this.coinRotationY = 0;
        this.coinRotationZ = 0;
        this.coinPositionY = 0;
        this.velocity = { x: 0, y: 0, z: 0 };
        this.angularVelocity = { x: 0, y: 0, z: 0 };
        this.bounceCount = 0;
        this.isSettling = false;

        // Reset ambient light intensity
        if (this.scene.children[0] && this.scene.children[0].isAmbientLight) {
            this.scene.children[0].intensity = 0.6;
        }

        // Remove final result light if it exists
        if (this.finalResultLight) {
            this.scene.remove(this.finalResultLight);
            this.scene.remove(this.finalResultLight.target);
            this.finalResultLight = null;
        }
    }

    // Pulse effect for coin interaction
    pulseEffect() {
        if (this.isFlipping) return;

        const originalScale = this.coin.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(1.1);

        // Animate scale up and down
        const duration = 300;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            let scale;
            if (progress < 0.5) {
                // Scale up
                const t = progress * 2;
                scale = originalScale.clone().lerp(targetScale, t);
            } else {
                // Scale down
                const t = (progress - 0.5) * 2;
                scale = targetScale.clone().lerp(originalScale, t);
            }

            this.coin.scale.copy(scale);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.coin.scale.copy(originalScale);
            }
        };

        animate();
    }

    // Resize handler
    resize() {
        if (this.camera && this.renderer) {
            this.camera.aspect = this.canvas.clientWidth / this.canvas.clientHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
        }
    }
}

/**
 * CoinFlipper Main Class - คลาสหลักสำหรับใช้งานใน Vue.js
 */
class CoinFlipper {
    constructor(canvasId, options = {}) {
        this.options = {
            autoLoadThreeJS: true,
            threeJSCDN: 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js',
            idleSpeed: 0.03,
            flipDuration: 3000,     // เพิ่มจาก 2000 เป็น 3000 มิลลิวินาที
            enableSound: true,
            usePatterns: true,
            ...options
        };

        this.canvasId = canvasId;
        this.audioManager = null;
        this.coinRenderer = null;
        this.isInitialized = false;
        this.initPromise = null;

        // Auto-initialize if Three.js is available
        if (THREE || !this.options.autoLoadThreeJS) {
            this.init();
        } else {
            this.initPromise = this.loadThreeJS().then(() => this.init());
        }
    }

    // โหลด Three.js จาก CDN
    async loadThreeJS() {
        if (typeof window === 'undefined') return;

        return new Promise((resolve, reject) => {
            if (window.THREE) {
                THREE = window.THREE;
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = this.options.threeJSCDN;
            script.onload = () => {
                THREE = window.THREE;
                resolve();
            };
            script.onerror = () => reject(new Error('Failed to load Three.js'));
            document.head.appendChild(script);
        });
    }

    // เริ่มต้นระบบ
    async init() {
        if (this.isInitialized) return;

        try {
            // Initialize audio manager
            if (this.options.enableSound) {
                this.audioManager = new AudioManager();
            }

            // Initialize coin renderer
            this.coinRenderer = new CoinRenderer(this.canvasId, {
                idleSpeed: this.options.idleSpeed,
                flipDuration: this.options.flipDuration
            });

            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize CoinFlipper:', error);
            throw error;
        }
    }

    // รอให้ระบบพร้อมใช้งาน
    async ready() {
        if (this.initPromise) {
            await this.initPromise;
        }
        if (!this.isInitialized) {
            await this.init();
        }
    }

    // เริ่ม idle animation (เหรียญหมุนเบาๆ)
    async startIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.startIdle();
        }
    }

    // หยุด idle animation
    async stopIdle() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.stopIdle();
        }
    }

    /**
     * ทอยเหรียญ - เมธอดหลักสำหรับการโยนเหรียญ
     * @param {string|null} result - ผลลัพธ์ที่ต้องการ ('heads', 'tails', หรือ null สำหรับสุ่ม)
     * @param {boolean|string} playSoundOrWinLose - เล่นเสียงหรือไม่ (boolean) หรือ win/lose state (string)
     * @param {string|null} winLoseState - สถานะ win/lose ('win', 'lose', หรือ null) - ใช้เมื่อ param ที่ 2 เป็น boolean
     * @returns {Promise<string>} - Promise ที่ resolve เมื่อการโยนเสร็จสิ้น พร้อมผลลัพธ์
     */
    async toss(result = null, playSoundOrWinLose = true, winLoseState = null) {
        // รอให้ระบบพร้อมใช้งาน
        await this.ready();

        // ตรวจสอบว่า CoinRenderer ถูกสร้างแล้ว
        if (!this.coinRenderer) {
            throw new Error('CoinRenderer not initialized');
        }

        // จัดการ parameters เพื่อรองรับทั้งรูปแบบเก่าและใหม่
        let playSound = true;
        let finalWinLoseState = null;

        if (typeof playSoundOrWinLose === 'boolean') {
            // รูปแบบเก่า: toss(result, playSound, winLoseState)
            playSound = playSoundOrWinLose;
            finalWinLoseState = winLoseState;
        } else if (typeof playSoundOrWinLose === 'string') {
            // รูปแบบใหม่: toss(result, winLoseState)
            playSound = true; // เล่นเสียงเสมอในรูปแบบใหม่
            finalWinLoseState = playSoundOrWinLose;
        }

        // === คำนวณจำนวน bounce สำหรับเสียง ===
        let soundBounceCount = null;
        if (playSound && this.audioManager) {
            // กำหนดผลลัพธ์สำหรับการคำนวณเสียง
            const soundResult = result || (Math.random() < 0.5 ? 'heads' : 'tails');
            soundBounceCount = this.coinRenderer.selectBounceCount(soundResult);

            console.log('🎵 Sound Bounce Calculation:', {
                requestedResult: result,
                soundResult: soundResult,
                soundBounceCount: soundBounceCount,
                currentSettingsPatterns: this.coinRenderer.options.settingsPatterns
            });
        }

        // === เล่นเสียงการทอย (ถ้าเปิดใช้งาน) ===
        if (playSound && this.audioManager) {
            this.audioManager.resumeAudioContext();    // เปิดใช้งาน AudioContext

            // ตรวจสอบว่าใช้ pattern หรือไม่
            let audioTiming = null;
            if (this.coinRenderer.options.usePatterns && this.coinRenderer.options.settingsPatterns[result]) {
                audioTiming = this.coinRenderer.options.settingsPatterns[result].audioTiming;
            }

            this.audioManager.generateFlipSound(soundBounceCount, audioTiming);     // เล่นเสียงการโยนพร้อมจำนวน bounce และ timing
        }

        // === ทำการทอยและรอผลลัพธ์ ===
        const finalResult = await this.coinRenderer.flipCoin(result, finalWinLoseState);

        return finalResult;
    }

    // เล่นเสียงชนะ
    async playWinSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateWinSound();
        }
    }

    // เล่นเสียงแพ้
    async playLoseSound() {
        await this.ready();
        if (this.audioManager) {
            this.audioManager.resumeAudioContext();
            this.audioManager.generateLoseSound();
        }
    }



    /**
     * เปิดใช้งาน settings patterns แทนการสุ่ม
     * @param {boolean} enable - เปิด (true) หรือปิด (false) การใช้ patterns
     */
    enableSettingsPatterns(enable = true) {
        this.options.usePatterns = enable;

        // อัพเดทใน CoinRenderer ถ้ามี
        if (this.coinRenderer) {
            this.coinRenderer.enableSettingsPatterns(enable);
        }

        console.log(`🎯 Settings patterns ${enable ? 'enabled' : 'disabled'}`);
    }

    /**
     * ตั้งค่า settings pattern ใหม่สำหรับผลลัพธ์ที่กำหนด
     * @param {string} result - ผลลัพธ์ ('heads' หรือ 'tails')
     * @param {Object} pattern - การตั้งค่า pattern ใหม่
     * @param {number} pattern.count - จำนวนการเด้ง
     * @param {number} pattern.damping - การลดพลังงานต่อการเด้ง (0-1)
     * @param {number} pattern.gravity - แรงโน้มถ่วง (ค่าลบ)
     * @param {Object} pattern.audioTiming - การตั้งค่าเวลาเสียง
     * @param {number} pattern.audioTiming.baseDelay - เวลาเริ่มต้น
     * @param {number} pattern.audioTiming.interval - ช่วงเวลาระหว่างการเด้ง
     * @param {number} pattern.audioTiming.decayFactor - การลดลงของช่วงเวลา
     */
    setSettingsPattern(result, pattern) {
        if (!this.options.settingsPatterns) {
            this.options.settingsPatterns = {};
        }

        this.options.settingsPatterns[result] = {
            count: pattern.count || 5,
            damping: pattern.damping || 0.7,
            gravity: pattern.gravity || -0.02,
            audioTiming: {
                baseDelay: pattern.audioTiming?.baseDelay || 0.4,
                interval: pattern.audioTiming?.interval || 0.25,
                decayFactor: pattern.audioTiming?.decayFactor || 0.85
            }
        };

        // อัพเดทใน CoinRenderer ถ้ามี
        if (this.coinRenderer) {
            this.coinRenderer.setSettingsPattern(result, pattern);
        }

        console.log(`🎯 Settings pattern set for ${result}:`, this.options.settingsPatterns[result]);
    }

    /**
     * ดึงค่า settings patterns ปัจจุบัน
     * @returns {Object} - object ที่มี patterns สำหรับ heads และ tails
     */
    getSettingsPatterns() {
        return this.options.settingsPatterns ? { ...this.options.settingsPatterns } : {};
    }

    /**
     * ตรวจสอบว่าใช้ settings patterns หรือไม่
     * @returns {boolean} - true ถ้าใช้ patterns, false ถ้าใช้การสุ่ม
     */
    isUsingSettingsPatterns() {
        return !!this.options.usePatterns;
    }

    // รีเซ็ตเหรียญกลับสู่สถานะเริ่มต้น
    async resetCoin() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resetCoin();
        }
    }

    // เอฟเฟกต์ pulse เมื่อคลิกเหรียญ
    async pulseEffect() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.pulseEffect();
        }
    }

    // ปรับขนาดตาม canvas
    async resize() {
        await this.ready();
        if (this.coinRenderer) {
            this.coinRenderer.resize();
        }
    }

    // ทำลายทุกอย่าง
    destroy() {
        if (this.coinRenderer) {
            this.coinRenderer.destroy();
            this.coinRenderer = null;
        }
        this.audioManager = null;
        this.isInitialized = false;
    }

    // ตรวจสอบสถานะ
    get status() {
        return {
            isInitialized: this.isInitialized,
            hasAudio: !!this.audioManager,
            hasRenderer: !!this.coinRenderer,
            isFlipping: this.coinRenderer ? this.coinRenderer.isFlipping : false,
            isIdle: this.coinRenderer ? this.coinRenderer.isIdle : false
        };
    }
}

// Export สำหรับใช้ใน different environments
if (typeof module !== 'undefined' && module.exports) {
    // Node.js/CommonJS
    module.exports = { CoinFlipper, AudioManager, CoinRenderer };
} else if (typeof define === 'function' && define.amd) {
    // AMD
    define([], function () {
        return { CoinFlipper, AudioManager, CoinRenderer };
    });
} else {
    // Browser global
    window.CoinFlipper = CoinFlipper;
    window.CoinFlipperAudioManager = AudioManager;
    window.CoinFlipperRenderer = CoinRenderer;
}
