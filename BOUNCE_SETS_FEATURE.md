# 🎯 Bounce Sets Feature

ฟีเจอร์ใหม่ที่ให้คุณสามารถกำหนด set ของจำนวนการ bounce สำหรับผลลัพธ์แต่ละแบบ (หัว/ก้อย) ได้

## 📋 คุณสมบัติ

- **กำหนด Bounce Sets**: ตั้งค่า set ของจำนวนการ bounce สำหรับ heads และ tails แยกกัน
- **การสุ่มแบบสมจริง**: ระบบจะสุ่มเลือกจำนวนการ bounce จาก set ที่กำหนด
- **เสียงที่สอดคล้อง**: เสียงการเด้งจะปรับตามจำนวนการ bounce ที่เลือก
- **การตั้งค่าแบบ Dynamic**: สามารถเปลี่ยนแปลง bounce sets ได้ระหว่างการใช้งาน

## 🚀 การใช้งาน

### การตั้งค่าเริ่มต้น

```javascript
const coinFlipper = new CoinFlipper('canvasId', {
    bounceSets: {
        heads: [3, 5, 7],   // หัว: สุ่มจาก 3, 5, หรือ 7 ครั้ง
        tails: [4, 6, 8]    // ก้อย: สุ่มจาก 4, 6, หรือ 8 ครั้ง
    }
});
```

### การเปลี่ยนแปลง Bounce Sets

```javascript
// อัพเดท bounce sets
coinFlipper.setBounceSets({
    heads: [2, 4, 6, 8],
    tails: [3, 5, 7, 9]
});

// ดูการตั้งค่าปัจจุบัน
const currentSets = coinFlipper.getBounceSets();
console.log(currentSets);
// Output: { heads: [2, 4, 6, 8], tails: [3, 5, 7, 9] }
```

### การทอยเหรียญ

```javascript
// สุ่มทอย (ระบบจะเลือก bounce count ตามผลลัพธ์)
const result = await coinFlipper.toss();

// บังคับผลลัพธ์ (ระบบจะเลือก bounce count จาก set ของผลลัพธ์นั้น)
const headsResult = await coinFlipper.toss('heads'); // จะเด้งตาม heads set
const tailsResult = await coinFlipper.toss('tails'); // จะเด้งตาม tails set
```

## 🎵 การทำงานของเสียง

เสียงการเด้งจะถูกสร้างขึ้นแบบ dynamic ตามจำนวนการ bounce ที่เลือก:

```javascript
// เสียงจะมีการเด้ง 5 ครั้งถ้าระบบเลือก 5 จาก heads set
await coinFlipper.toss('heads'); // ถ้า heads set = [3, 5, 7]
```

## 📊 ตัวอย่างการใช้งาน

### ตัวอย่างที่ 1: เกมที่ต้องการความแตกต่าง

```javascript
const gameCoins = new CoinFlipper('gameCanvas', {
    bounceSets: {
        heads: [2, 3],      // หัว: เด้งน้อย (ดูเร็ว)
        tails: [6, 7, 8]    // ก้อย: เด้งมาก (ดูนาน)
    }
});
```

### ตัวอย่างที่ 2: การจำลองเหรียญจริง

```javascript
const realisticCoin = new CoinFlipper('realisticCanvas', {
    bounceSets: {
        heads: [4, 5, 6, 7],    // การกระจายแบบธรรมชาติ
        tails: [4, 5, 6, 7]     // เหมือนกันทั้งสองด้าน
    }
});
```

### ตัวอย่างที่ 3: เอฟเฟกต์พิเศษ

```javascript
const specialCoin = new CoinFlipper('specialCanvas', {
    bounceSets: {
        heads: [1],         // หัว: เด้งครั้งเดียว (ดูมีพลัง)
        tails: [10, 12, 15] // ก้อย: เด้งมากมาย (ดูวุ่นวาย)
    }
});
```

## 🔧 API Reference

### `setBounceSets(bounceSets)`

ตั้งค่า bounce sets ใหม่

**Parameters:**
- `bounceSets` (Object): Object ที่มี heads และ tails arrays
  - `bounceSets.heads` (Array): Array ของจำนวนการ bounce สำหรับ heads
  - `bounceSets.tails` (Array): Array ของจำนวนการ bounce สำหรับ tails

**Example:**
```javascript
coinFlipper.setBounceSets({
    heads: [3, 5, 7],
    tails: [4, 6, 8]
});
```

### `getBounceSets()`

ดึงค่า bounce sets ปัจจุบัน

**Returns:** Object ที่มี heads และ tails arrays

**Example:**
```javascript
const sets = coinFlipper.getBounceSets();
console.log(sets.heads); // [3, 5, 7]
console.log(sets.tails); // [4, 6, 8]
```

## 🎮 ตัวอย่างการใช้งานแบบเต็ม

ดูตัวอย่างการใช้งานแบบเต็มได้ที่: `examples/bounce-sets-example.html`

## 📝 หมายเหตุ

1. **ค่าเริ่มต้น**: หากไม่กำหนด bounce sets ระบบจะใช้ค่าเริ่มต้น `heads: [3,5,7]` และ `tails: [4,6,8]`
2. **การตรวจสอบ**: ระบบจะตรวจสอบว่า arrays ไม่ว่างและมีเฉพาะตัวเลขบวก
3. **การสุ่ม**: การเลือกจำนวน bounce จาก set จะใช้ `Math.random()` แบบสุ่มเท่าเทียม
4. **เสียง**: เสียงการเด้งจะถูกสร้างขึ้นแบบ dynamic ตามจำนวนที่เลือก

## 🐛 การแก้ไขปัญหา

### ปัญหา: เสียงไม่ตรงกับการเด้ง
**วิธีแก้**: ตรวจสอบว่า bounce sets ถูกส่งไปยัง AudioManager อย่างถูกต้อง

### ปัญหา: การเด้งไม่หยุด
**วิธีแก้**: ตรวจสอบว่าค่าใน bounce sets เป็นตัวเลขบวกและไม่เกิน 20

### ปัญหา: ไม่สามารถอัพเดท bounce sets ได้
**วิธีแก้**: ตรวจสอบว่า CoinFlipper ถูก initialize แล้วก่อนเรียก `setBounceSets()`
