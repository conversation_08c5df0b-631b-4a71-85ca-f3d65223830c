// Test backward compatibility of the coin flipper API
const fs = require('fs');

console.log('🔄 Testing Backward Compatibility');
console.log('=================================');

try {
    const jsContent = fs.readFileSync('src/coin-flipper.js', 'utf8');
    
    // Test 1: Check if old toss(result) still works
    const hasTossWithResult = jsContent.includes('async toss(result = null');
    console.log('✅ Test 1 - toss(result) method signature:', hasTossWithResult ? 'PASS' : 'FAIL');
    
    // Test 2: Check if old toss(result, playSound) still works
    const hasPlaySoundHandling = jsContent.includes('playSoundOrWinLose') && jsContent.includes('typeof playSoundOrWinLose === \'boolean\'');
    console.log('✅ Test 2 - toss(result, playSound) compatibility:', hasPlaySoundHandling ? 'PASS' : 'FAIL');
    
    // Test 3: Check if flip<PERSON>oin still works without winLoseState
    const hasFlipCoinDefault = jsContent.includes('winLoseState = null');
    console.log('✅ Test 3 - flipCoin backward compatibility:', hasFlipCoinDefault ? 'PASS' : 'FAIL');
    
    // Test 4: Check if enhanceFinalResult works without winLoseState
    const hasEnhanceDefault = jsContent.includes('enhanceFinalResult(winLoseState = null)');
    console.log('✅ Test 4 - enhanceFinalResult backward compatibility:', hasEnhanceDefault ? 'PASS' : 'FAIL');
    
    // Test 5: Check if existing methods are preserved
    const hasStartIdle = jsContent.includes('startIdle()');
    const hasStopIdle = jsContent.includes('stopIdle()');
    const hasPlayWinSound = jsContent.includes('playWinSound()');
    const hasPlayLoseSound = jsContent.includes('playLoseSound()');
    
    console.log('✅ Test 5 - startIdle method:', hasStartIdle ? 'PASS' : 'FAIL');
    console.log('✅ Test 6 - stopIdle method:', hasStopIdle ? 'PASS' : 'FAIL');
    console.log('✅ Test 7 - playWinSound method:', hasPlayWinSound ? 'PASS' : 'FAIL');
    console.log('✅ Test 8 - playLoseSound method:', hasPlayLoseSound ? 'PASS' : 'FAIL');
    
    // Test 9: Check if status property is preserved
    const hasStatusProperty = jsContent.includes('get status()');
    console.log('✅ Test 9 - status property:', hasStatusProperty ? 'PASS' : 'FAIL');
    
    // Test 10: Check if destroy method is preserved
    const hasDestroyMethod = jsContent.includes('destroy()');
    console.log('✅ Test 10 - destroy method:', hasDestroyMethod ? 'PASS' : 'FAIL');
    
    console.log('\n📊 Backward Compatibility Summary:');
    const tests = [
        hasTossWithResult,
        hasPlaySoundHandling,
        hasFlipCoinDefault,
        hasEnhanceDefault,
        hasStartIdle,
        hasStopIdle,
        hasPlayWinSound,
        hasPlayLoseSound,
        hasStatusProperty,
        hasDestroyMethod
    ];
    
    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;
    
    console.log(`Passed: ${passedTests}/${totalTests} tests`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All backward compatibility tests passed!');
        console.log('✅ Existing code should continue to work without changes.');
    } else {
        console.log('⚠️  Some backward compatibility tests failed.');
    }
    
} catch (error) {
    console.error('❌ Error running backward compatibility tests:', error.message);
}

console.log('\n📋 API Usage Examples:');
console.log('======================');
console.log('// Old API (still works):');
console.log('await coinFlipper.toss();                    // Random result');
console.log('await coinFlipper.toss("heads");             // Force heads');
console.log('await coinFlipper.toss("heads", false);      // Force heads, no sound');
console.log('');
console.log('// New API (win/lose scenes):');
console.log('await coinFlipper.toss("heads", "win");      // Heads with WIN scene');
console.log('await coinFlipper.toss("tails", "lose");     // Tails with LOSE scene');
console.log('await coinFlipper.toss(null, "win");         // Random with WIN scene');
console.log('');
console.log('// Mixed API (backward compatible):');
console.log('await coinFlipper.toss("heads", true, "win"); // Heads, with sound, WIN scene');

console.log('\n✨ Backward compatibility testing complete!');
