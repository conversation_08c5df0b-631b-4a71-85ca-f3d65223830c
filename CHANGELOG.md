# Changelog

All notable changes to the CoinFlipper Module will be documented in this file.

## [1.0.0] - 2025-01-08

### Added
- 🎯 **Idle Animation**: เหรียญหมุนไปเรื่อยๆ ด้วยความเร็วที่กำหนดได้
- 🎲 **Toss Animation**: การแสดงเหรียญกำลังถูกทอยพร้อมเสียงและกำหนดผลลัพธ์ได้
- 🔊 **Sound Effects**: เสียงการทอย, เสียงชนะ, เสียงแพ้
- 🎨 **3D Graphics**: ใช้ Three.js สำหรับ animation ที่สมจริง
- 📱 **Responsive**: รองรับการใช้งานบนอุปกรณ์ต่างๆ
- 🔧 **TypeScript Support**: มี type definitions ครบถ้วน
- 🚀 **Easy Integration**: สามารถ import ใช้ใน Vue.js ได้ทันที
- 📦 **Self-contained**: รวม Three.js ไว้ในโมดูลแล้ว ไม่ต้องติดตั้งแยก

### Features
- **CoinFlipper Class**: คลาสหลักสำหรับการใช้งาน
- **AudioManager Class**: จัดการเสียงทั้งหมด
- **CoinRenderer Class**: จัดการ 3D animation ด้วย Three.js
- **Auto Three.js Loading**: โหลด Three.js อัตโนมัติจาก CDN
- **Customizable Options**: ปรับแต่งความเร็ว, ระยะเวลา, เสียง
- **Memory Management**: จัดการ resources อย่างมีประสิทธิภาพ

### API Methods
- `ready()`: รอให้โมดูลพร้อมใช้งาน
- `startIdle()`: เริ่ม idle animation
- `stopIdle()`: หยุด idle animation
- `toss(result?, playSound?)`: ทอยเหรียญ
- `playWinSound()`: เล่นเสียงชนะ
- `playLoseSound()`: เล่นเสียงแพ้
- `resize()`: ปรับขนาดตาม canvas
- `destroy()`: ทำลายและทำความสะอาด

### Examples
- Vue.js Component (JavaScript)
- Vue.js Component (TypeScript)
- HTML Demo Page

### Documentation
- Complete API Documentation
- Usage Examples
- TypeScript Definitions
- Best Practices Guide

### Browser Support
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

---

## Future Plans

### [1.1.0] - Planned
- [ ] Custom coin textures support
- [ ] More sound effect options
- [ ] Animation presets
- [ ] Performance optimizations

### [1.2.0] - Planned
- [ ] Multiple coin support
- [ ] Custom physics parameters
- [ ] Mobile touch gestures
- [ ] WebXR support

---

## Contributing

If you'd like to contribute to this project, please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - See LICENSE file for details
