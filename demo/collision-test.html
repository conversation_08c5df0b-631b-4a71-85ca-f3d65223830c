<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coin Collision Test - ทดสอบการชนกับพื้น</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        #coinCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            background: #f8f9fa;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }

        .debug-info h3 {
            margin-top: 0;
            color: #495057;
            font-family: 'Segoe UI', sans-serif;
        }

        .log-entry {
            margin: 5px 0;
            padding: 3px 0;
            border-bottom: 1px solid #eee;
        }

        .log-bounce { color: #e74c3c; }
        .log-camera { color: #3498db; }
        .log-force { color: #f39c12; }
        .log-correction { color: #27ae60; }

        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }

        .status.idle { background: #d4edda; color: #155724; }
        .status.flipping { background: #fff3cd; color: #856404; }
        .status.result { background: #cce7ff; color: #004085; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Coin Collision Test</h1>
        <p class="subtitle">ทดสอบการแก้ไขปัญหาเหรียญจมพื้นตอนกระเด้ง</p>
        
        <canvas id="coinCanvas" width="400" height="400"></canvas>
        
        <div class="controls">
            <button id="flipBtn">🎲 ทอยเหรียญ</button>
            <button id="multiFlipBtn">🎯 ทอย 5 ครั้ง</button>
            <button id="clearLogBtn">🧹 ล้าง Log</button>
        </div>

        <div id="status" class="status idle">สถานะ: พร้อมทอย</div>

        <div class="debug-info">
            <h3>🔍 Debug Log - ติดตามการชนกับพื้น</h3>
            <div id="debugLog"></div>
        </div>
    </div>

    <!-- Three.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Coin Flipper Module -->
    <script src="../src/coin-flipper.js"></script>

    <script>
        let coinFlipper;
        let flipCount = 0;
        let multiFlipActive = false;
        let multiFlipRemaining = 0;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('coinCanvas');
            const flipBtn = document.getElementById('flipBtn');
            const multiFlipBtn = document.getElementById('multiFlipBtn');
            const clearLogBtn = document.getElementById('clearLogBtn');
            const statusDiv = document.getElementById('status');
            const debugLog = document.getElementById('debugLog');

            // สร้าง CoinFlipper instance
            coinFlipper = new CoinFlipper(canvas, {
                flipDuration: 3000,
                idleSpeed: 0.02
            });

            // Override console.log เพื่อจับ debug messages
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                
                // จับ log messages ที่เกี่ยวข้องกับการชนและการแก้ไข
                const message = args.join(' ');
                if (message.includes('🏀 Bounce collision') || 
                    message.includes('🔧 Additional position') ||
                    message.includes('📷 Camera stopped') ||
                    message.includes('🎯 Forcing coin')) {
                    
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry';
                    
                    if (message.includes('🏀 Bounce')) {
                        logEntry.className += ' log-bounce';
                    } else if (message.includes('📷 Camera')) {
                        logEntry.className += ' log-camera';
                    } else if (message.includes('🎯 Forcing')) {
                        logEntry.className += ' log-force';
                    } else if (message.includes('🔧 Additional')) {
                        logEntry.className += ' log-correction';
                    }
                    
                    logEntry.textContent = new Date().toLocaleTimeString() + ': ' + message;
                    debugLog.appendChild(logEntry);
                    debugLog.scrollTop = debugLog.scrollHeight;
                }
            };

            // Event listeners
            flipBtn.addEventListener('click', async () => {
                if (coinFlipper.isFlipping) return;
                
                flipCount++;
                updateStatus('flipping', `กำลังทอยครั้งที่ ${flipCount}...`);
                flipBtn.disabled = true;
                multiFlipBtn.disabled = true;

                try {
                    const result = await coinFlipper.flip();
                    updateStatus('result', `ผลลัพธ์: ${result === 'heads' ? 'หัว' : 'ก้อย'}`);
                    
                    setTimeout(() => {
                        if (!multiFlipActive) {
                            updateStatus('idle', 'พร้อมทอยครั้งต่อไป');
                            flipBtn.disabled = false;
                            multiFlipBtn.disabled = false;
                        }
                    }, 2000);
                } catch (error) {
                    console.error('Flip error:', error);
                    updateStatus('idle', 'เกิดข้อผิดพลาด - พร้อมทอยใหม่');
                    flipBtn.disabled = false;
                    multiFlipBtn.disabled = false;
                }
            });

            multiFlipBtn.addEventListener('click', async () => {
                if (coinFlipper.isFlipping || multiFlipActive) return;
                
                multiFlipActive = true;
                multiFlipRemaining = 5;
                flipBtn.disabled = true;
                multiFlipBtn.disabled = true;

                for (let i = 0; i < 5; i++) {
                    flipCount++;
                    multiFlipRemaining--;
                    updateStatus('flipping', `ทอยต่อเนื่อง ${i + 1}/5 (เหลือ ${multiFlipRemaining})`);
                    
                    try {
                        const result = await coinFlipper.flip();
                        updateStatus('result', `ครั้งที่ ${i + 1}: ${result === 'heads' ? 'หัว' : 'ก้อย'}`);
                        
                        // รอสักครู่ก่อนทอยครั้งต่อไป
                        if (i < 4) {
                            await new Promise(resolve => setTimeout(resolve, 1500));
                        }
                    } catch (error) {
                        console.error('Multi-flip error:', error);
                        break;
                    }
                }

                multiFlipActive = false;
                updateStatus('idle', `ทอยครบ 5 ครั้งแล้ว - พร้อมทอยใหม่`);
                flipBtn.disabled = false;
                multiFlipBtn.disabled = false;
            });

            clearLogBtn.addEventListener('click', () => {
                debugLog.innerHTML = '';
            });

            function updateStatus(state, message) {
                statusDiv.className = `status ${state}`;
                statusDiv.textContent = `สถานะ: ${message}`;
            }

            // เริ่ม idle animation
            coinFlipper.startIdleAnimation();
            updateStatus('idle', 'พร้อมทอย - ดู Debug Log เพื่อติดตามการชนกับพื้น');
        });
    </script>
</body>
</html>
